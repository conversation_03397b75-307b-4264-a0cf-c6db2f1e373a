{"arrowParens": "always", "semi": false, "tabWidth": 2, "printWidth": 80, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "endOfLine": "lf", "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^path$", "^vite$", "^@vitejs/(.*)$", "^react$", "^react-dom/client$", "^react/(.*)$", "^globals$", "^zod$", "^axios$", "^date-fns$", "^js-cookie$", "^react-hook-form$", "^use-intl$", "^@radix-ui/(.*)$", "^@hookform/resolvers/zod$", "^@tabler/icons-react$", "<THIRD_PARTY_MODULES>", "^@/assets/(.*)", "^@/api/(.*)$", "^@/stores/(.*)$", "^@/lib/(.*)$", "^@/utils/(.*)$", "^@/constants/(.*)$", "^@/context/(.*)$", "^@/hooks/(.*)$", "^@/components/layouts/(.*)$", "^@/components/ui/(.*)$", "^@/components/errors/(.*)$", "^@/components/(.*)$", "^@/features/(.*)$", "^[./]"]}