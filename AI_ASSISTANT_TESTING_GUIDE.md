# AI助手功能测试指南

## 概述
本指南提供了AI助手功能的手动测试步骤，确保所有功能正常工作。

## 前置条件
1. 项目已启动 (`pnpm dev`)
2. 浏览器访问 `http://localhost:5176`
3. 准备有效的API密钥（OpenAI、Google Gemini等）

## 测试步骤

### 1. 基础界面测试
- [ ] 访问帮助中心页面 (`/help-center`)
- [ ] 确认页面显示"AI助手"组件
- [ ] 确认显示配置提示："请先配置AI服务以开始使用助手功能"

### 2. 配置功能测试

#### 2.1 OpenAI配置测试
- [ ] 选择"OpenAI"作为服务提供商
- [ ] 输入有效的OpenAI API密钥 (格式: sk-...)
- [ ] 选择模型 (如: gpt-3.5-turbo)
- [ ] 调整温度设置 (0.1-1.0)
- [ ] 点击"保存配置"
- [ ] 确认配置保存成功

#### 2.2 Google Gemini配置测试
- [ ] 选择"Google Gemini"作为服务提供商
- [ ] 输入有效的Google API密钥
- [ ] 选择模型 (如: gemini-pro)
- [ ] 点击"保存配置"
- [ ] 确认配置保存成功

#### 2.3 自定义API配置测试
- [ ] 选择"自定义API"作为服务提供商
- [ ] 输入API密钥
- [ ] 输入自定义API端点URL
- [ ] 输入模型名称
- [ ] 点击"保存配置"
- [ ] 确认配置保存成功

### 3. 聊天功能测试

#### 3.1 基础聊天测试
- [ ] 配置完成后，确认自动切换到"聊天"标签
- [ ] 确认显示欢迎消息："您好！我是您的AI助手，有什么可以帮助您的吗？"
- [ ] 确认显示预设问题建议
- [ ] 在输入框中输入简单问题："你好"
- [ ] 点击发送按钮或按Enter键
- [ ] 确认消息发送成功
- [ ] 确认收到AI回复

#### 3.2 预设问题测试
- [ ] 点击"帮我解释一下这个系统的功能"
- [ ] 确认问题自动填入并发送
- [ ] 确认收到相关系统功能的回复
- [ ] 点击"如何使用MQTT功能？"
- [ ] 确认收到MQTT相关的回复
- [ ] 点击"用户管理有哪些功能？"
- [ ] 确认收到用户管理相关的回复

#### 3.3 消息操作测试
- [ ] 发送一条消息后，确认显示复制按钮
- [ ] 点击复制按钮，确认消息复制到剪贴板
- [ ] 确认显示重新生成按钮
- [ ] 点击重新生成按钮，确认AI重新生成回复
- [ ] 确认用户消息显示编辑按钮
- [ ] 点击编辑按钮，修改消息内容
- [ ] 确认编辑后重新发送

#### 3.4 流式响应测试
- [ ] 发送较长的问题，观察AI回复是否逐字显示
- [ ] 在AI回复过程中，确认显示取消按钮
- [ ] 点击取消按钮，确认回复停止

### 4. 本地存储测试
- [ ] 配置AI服务后，刷新页面
- [ ] 确认配置信息保持不变
- [ ] 确认直接显示聊天界面（不需要重新配置）
- [ ] 在配置标签中确认之前的设置仍然存在

### 5. 标签切换测试
- [ ] 在聊天标签和配置标签之间切换
- [ ] 确认切换流畅，内容正确显示
- [ ] 在配置标签中修改设置
- [ ] 切换回聊天标签，确认新配置生效

### 6. 错误处理测试

#### 6.1 无效API密钥测试
- [ ] 输入无效的API密钥
- [ ] 尝试发送消息
- [ ] 确认显示适当的错误信息

#### 6.2 网络错误测试
- [ ] 断开网络连接
- [ ] 尝试发送消息
- [ ] 确认显示网络错误信息
- [ ] 恢复网络连接，确认功能恢复

#### 6.3 API配置验证测试
- [ ] 输入格式错误的API密钥
- [ ] 确认显示验证错误信息
- [ ] 输入正确格式的API密钥
- [ ] 确认验证通过

### 7. 响应式设计测试
- [ ] 在桌面浏览器中测试所有功能
- [ ] 调整浏览器窗口大小，确认界面适应
- [ ] 在移动设备或移动模式下测试
- [ ] 确认所有按钮和输入框可正常使用

### 8. 性能测试
- [ ] 发送多条消息，观察性能表现
- [ ] 确认长对话不会导致界面卡顿
- [ ] 确认内存使用合理

## 预期结果
- 所有配置功能正常工作
- AI聊天功能响应正确
- 本地存储功能正常
- 界面交互流畅
- 错误处理适当
- 响应式设计良好

## 常见问题排查

### 问题1：AI不回复
- 检查API密钥是否正确
- 检查网络连接
- 检查浏览器控制台错误信息

### 问题2：配置不保存
- 检查浏览器是否允许localStorage
- 清除浏览器缓存后重试

### 问题3：界面显示异常
- 检查浏览器控制台错误
- 确认所有依赖正确安装
- 重启开发服务器

## 测试完成标准
- [ ] 所有测试步骤通过
- [ ] 无严重错误或异常
- [ ] 用户体验良好
- [ ] 功能符合预期

## 备注
- 测试时请使用真实的API密钥以确保完整功能测试
- 建议在不同浏览器中进行测试
- 记录任何发现的问题或改进建议
