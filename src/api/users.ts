import apiClient from '@/lib/api'

// API响应基础接口
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 用户角色枚举
export type UserRole = 'admin' | 'user'

// 用户状态枚举
export type UserStatus = 'active' | 'inactive' | 'banned' | 'pending'

// 用户资料接口
export interface UserProfile {
  first_name?: string
  last_name?: string
  display_name?: string
  avatar?: string
  company?: string
  department?: string
}

// 用户响应接口
export interface UserResponse {
  id: string
  email: string
  role: UserRole
  status: UserStatus
  profile?: UserProfile
  created_at: string
  updated_at: string
  last_login_at?: string
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
}

// 获取用户列表查询参数
export interface GetUsersParams {
  page?: number
  page_size?: number
  role?: UserRole
  status?: UserStatus
  search?: string
}

// 更新用户请求接口
export interface UpdateUserRequest {
  email?: string
  role?: UserRole
  status?: UserStatus
  profile?: UserProfile
}

// 更新用户状态请求接口
export interface UpdateUserStatusRequest {
  status: UserStatus
}

// 用户管理API服务类
export class UsersAPI {
  /**
   * 获取用户列表
   */
  static async getUsers(params?: GetUsersParams): Promise<ApiResponse<PaginatedResponse<UserResponse>>> {
    const response = await apiClient.get('/admin/users', { params })
    return response.data
  }

  /**
   * 获取指定用户信息
   */
  static async getUser(id: string): Promise<ApiResponse<UserResponse>> {
    const response = await apiClient.get(`/admin/users/${id}`)
    return response.data
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<UserResponse>> {
    const response = await apiClient.put(`/admin/users/${id}`, data)
    return response.data
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(id: string, data: UpdateUserStatusRequest): Promise<ApiResponse> {
    const response = await apiClient.put(`/admin/users/${id}/status`, data)
    return response.data
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/admin/users/${id}`)
    return response.data
  }
}

// 导出类型
export type {
  ApiResponse,
}
