import apiClient from '@/lib/api'

// API响应基础接口
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 日志级别枚举
export type LogLevel = 'info' | 'warn' | 'error' | 'debug'

// 日志模块枚举
export type LogModule = 'auth' | 'mqtt' | 'system' | 'config'

// 日志操作枚举
export type LogAction = 'create' | 'update' | 'delete' | 'login'

// 日志条目接口
export interface LogEntry {
  id: string
  level: LogLevel
  module: LogModule
  action: LogAction
  message: string
  user_id?: string
  user_name?: string
  ip?: string
  user_agent?: string
  details?: Record<string, any>
  timestamp: string
}

// 分页响应接口
export interface PaginatedResponse<T> {
  list: T[]
  total: number
}

// 获取日志列表查询参数
export interface GetLogsParams {
  level?: LogLevel
  module?: LogModule
  action?: LogAction
  user_id?: string
  time_range?:string
  page?: number
  limit?: number
  search?: string  // 添加search参数支持搜索功能
}

// 日志统计接口
export interface LogStatistics {
  total_logs: number
  level_distribution: Record<LogLevel, number>
  module_distribution: Record<LogModule, number>
  hourly_distribution: Array<{
    hour: string
    count: number
  }>
}

// 日志清理请求接口
export interface CleanupLogsRequest {
  before: string // 清理时间点，格式：2006-01-02 15:04:05
}

// 日志清理响应接口
export interface CleanupLogsResponse {
  count: number
}

// 日志管理API服务类
export class LogsAPI {
  /**
   * 获取系统日志
   */
  static async getLogs(params?: GetLogsParams): Promise<ApiResponse<PaginatedResponse<LogEntry>>> {
    const response = await apiClient.get('/system/logs', { params })
    return response.data
  }

  /**
   * 获取日志统计
   */
  static async getLogStatistics(params?: {
    start_time?: string
    end_time?: string
  }): Promise<ApiResponse<LogStatistics>> {
    const response = await apiClient.get('/system/logs/statistics', { params })
    return response.data
  }

  /**
   * 清理历史日志
   */
  static async cleanupLogs(params: CleanupLogsRequest): Promise<ApiResponse<CleanupLogsResponse>> {
    const response = await apiClient.delete('/system/logs/cleanup', { params })
    return response.data
  }
}

// 导出类型
export type {
  ApiResponse,
}
