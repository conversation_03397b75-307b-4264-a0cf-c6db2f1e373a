import apiClient from '@/lib/api'
import type {
  DashboardData,
  ApiResponse,
} from '@/types/monitor'

// 监控面板数据
export const getMonitorDashboard = async (): Promise<DashboardData> => {
  const response = await apiClient.get<ApiResponse<DashboardData>>('/admin/dashboard')
  return response.data.data
}

// React Query hooks
export const monitorQueryKeys = {
  all: ['monitor'] as const,
  dashboard: () => [...monitorQueryKeys.all, 'dashboard'] as const,
}
