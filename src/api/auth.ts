import apiClient from '@/lib/api'
import { AuthUser, UserProfile } from '@/stores/authStore'

// API响应基础接口
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 登录请求接口
interface LoginRequest {
  email: string
  password: string
}

// 登录响应接口
interface LoginResponse {
  user: AuthUser
  access_token: string
  refresh_token: string
  expires_in: number
}

// 发送注册验证码请求接口
interface RegisterSendCodeRequest {
  email: string
  password: string
  role?: string
  profile?: UserProfile
}

// 验证注册验证码请求接口
interface RegisterVerifyRequest {
  email: string
  code: string
}

// 忘记密码请求接口
interface ForgotPasswordRequest {
  email: string
  send_code: boolean
  code?: string
}

// 修改密码请求接口
interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

// 更新用户资料请求接口
interface UpdateProfileRequest {
  profile?: UserProfile
}

// 认证API服务类
export class AuthAPI {
  /**
   * 用户登录
   */
  static async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await apiClient.post('/auth/login', data)
    return response.data
  }

  /**
   * 发送注册验证码
   */
  static async registerSendCode(data: RegisterSendCodeRequest): Promise<ApiResponse<{ email: string; code?: string }>> {
    const response = await apiClient.post('/auth/register/send-code', data)
    return response.data
  }

  /**
   * 验证注册验证码并完成注册
   */
  static async registerVerify(data: RegisterVerifyRequest): Promise<ApiResponse<AuthUser>> {
    const response = await apiClient.post('/auth/register/verify', data)
    return response.data
  }

  /**
   * 忘记密码
   */
  static async forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<{ email: string }>> {
    const response = await apiClient.post('/auth/forgot-password', data)
    return response.data
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(): Promise<ApiResponse<{ access_token: string; expires_in: number }>> {
    const response = await apiClient.post('/auth/refresh')
    return response.data
  }

  /**
   * 验证访问令牌
   */
  static async validateToken(): Promise<ApiResponse<{
    valid: boolean
    user_id: string
    email: string
    role: string
    expires_at: string
  }>> {
    const response = await apiClient.post('/auth/validate')
    return response.data
  }

  /**
   * 撤销当前访问令牌
   */
  static async revokeToken(): Promise<ApiResponse> {
    const response = await apiClient.post('/auth/revoke')
    return response.data
  }

  /**
   * 检查令牌状态
   */
  static async getTokenStatus(): Promise<ApiResponse<{
    active: boolean
    user_id: string
    expires_at: string
    issued_at: string
  }>> {
    const response = await apiClient.get('/auth/status')
    return response.data
  }

  /**
   * 获取当前用户资料
   */
  static async getProfile(): Promise<ApiResponse<AuthUser>> {
    const response = await apiClient.get('/users/profile')
    return response.data
  }

  /**
   * 更新用户资料
   */
  static async updateProfile(data: UpdateProfileRequest): Promise<ApiResponse<AuthUser>> {
    const response = await apiClient.put('/users/profile', data)
    return response.data
  }

  /**
   * 修改密码
   */
  static async changePassword(data: ChangePasswordRequest): Promise<ApiResponse> {
    const response = await apiClient.post('/users/change-password', data)
    return response.data
  }
}

// 导出类型
export type {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  RegisterSendCodeRequest,
  RegisterVerifyRequest,
  ForgotPasswordRequest,
  ChangePasswordRequest,
  UpdateProfileRequest,
}
