import apiClient from '@/lib/api'
import type {
  ApiResponse,
  PaginatedResponse,
  GroupsResponse,
  MembersResponse,
  RequestsResponse,
  TopicsResponse,
  Group,
  GroupMember,
  GroupTopic,
  TopicPermission,
  JoinRequest,
  TopicMessage,
  TopicDataResponse,
  DeleteTopicDataResponse,
  AlertRule,
  AlertRecord,


  GroupDetail,
  CreateGroupRequest,
  UpdateGroupRequest,
  JoinGroupRequest,
  ReviewJoinRequest,
  CreateTopicRequest,
  SetPermissionRequest,
  CreateAlertRuleRequest,
  UpdateAlertRuleRequest,
  TestAlertRuleRequest,
  GetGroupsParams,
  GetMembersParams,
  GetRequestsParams,
  GetPermissionsParams,
  GetTopicDataParams,
  DeleteTopicDataParams,
  GetAlertRulesParams,
  GetAlertRecordsParams,
} from '@/types/groups'

// 分组管理API服务类
export class GroupsAPI {
  // ==================== 分组管理 ====================
  
  /**
   * 获取分组列表
   */
  static async getGroups(params?: GetGroupsParams): Promise<ApiResponse<GroupsResponse>> {
    const response = await apiClient.get('/groups', { params })
    return response.data
  }

  /**
   * 获取用户分组列表
   */
  static async getMyGroups(): Promise<ApiResponse<Group[]>> {
    const response = await apiClient.get('/groups/my')
    return response.data
  }

  /**
   * 获取指定分组信息
   */
  static async getGroup(groupId: string): Promise<ApiResponse<GroupDetail>> {
    const response = await apiClient.get(`/groups/${groupId}`)
    return response.data
  }

  /**
   * 创建分组
   */
  static async createGroup(data: CreateGroupRequest): Promise<ApiResponse<Group>> {
    const response = await apiClient.post('/groups', data)
    return response.data
  }

  /**
   * 更新分组信息
   */
  static async updateGroup(groupId: string, data: UpdateGroupRequest): Promise<ApiResponse<Group>> {
    const response = await apiClient.put(`/groups/${groupId}`, data)
    return response.data
  }

  /**
   * 删除分组
   */
  static async deleteGroup(groupId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/groups/${groupId}`)
    return response.data
  }

  // ==================== 成员管理 ====================

  /**
   * 获取分组成员列表
   */
  static async getMembers(groupId: string, params?: GetMembersParams): Promise<ApiResponse<MembersResponse>> {
    const response = await apiClient.get(`/groups/${groupId}/members`, { params })
    return response.data
  }

  /**
   * 申请加入分组
   */
  static async joinGroup(data: JoinGroupRequest): Promise<ApiResponse> {
    const response = await apiClient.post('/groups/join', data)
    return response.data
  }

  /**
   * 获取分组的加入申请列表
   */
  static async getJoinRequests(groupId: string, params?: GetRequestsParams): Promise<ApiResponse<PaginatedResponse<JoinRequest>>> {
    const response = await apiClient.get(`/groups/${groupId}/requests`, { params })
    return response.data
  }

  /**
   * 获取所有分组的加入申请列表（仅创建者可查看自己创建的分组的申请）
   */
  static async getAllJoinRequests(params?: GetRequestsParams): Promise<ApiResponse<RequestsResponse>> {
    const response = await apiClient.get('/groups/requests', { params })
    return response.data
  }

  /**
   * 审核加入申请
   */
  static async reviewJoinRequest(requestId: string, data: ReviewJoinRequest): Promise<ApiResponse> {
    const response = await apiClient.put(`/groups/requests/${requestId}/review`, data)
    return response.data
  }

  /**
   * 移除成员
   */
  static async removeMember(groupId: string, userId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/groups/${groupId}/members/${userId}`)
    return response.data
  }

  /**
   * 退出分组
   */
  static async leaveGroup(groupId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/groups/${groupId}/leave`)
    return response.data
  }

  // ==================== Topic管理 ====================

  /**
   * 获取分组Topic列表
   */
  static async getTopics(groupId: string): Promise<ApiResponse<TopicsResponse>> {
    const response = await apiClient.get(`/groups/${groupId}/topics`)
    return response.data
  }

  /**
   * 创建Topic
   */
  static async createTopic(groupId: string, data: CreateTopicRequest): Promise<ApiResponse<GroupTopic>> {
    const response = await apiClient.post(`/groups/${groupId}/topics`, data)
    return response.data
  }

  /**
   * 删除Topic
   */
  static async deleteTopic(groupId: string, topicId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/groups/${groupId}/topics/${topicId}`)
    return response.data
  }

  // ==================== 权限管理 ====================

  /**
   * 获取Topic权限列表
   */
  static async getPermissions(
    groupId: string,
    fullTopic: string,
    params?: GetPermissionsParams
  ): Promise<ApiResponse<{ permissions: TopicPermission[]; total: number; page: number; size: number }>> {
    const queryParams = { ...params, fullTopic }
    const response = await apiClient.get(`/groups/${groupId}/permissions`, { params: queryParams })
    return response.data
  }

  /**
   * 设置Topic权限
   */
  static async setPermission(groupId: string, data: SetPermissionRequest): Promise<ApiResponse> {
    const response = await apiClient.post(`/groups/${groupId}/permissions`, data)
    return response.data
  }

  /**
   * 移除Topic权限
   */
  static async removePermission(groupId: string, userId: string, fullTopic: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/groups/${groupId}/permissions/${userId}?fullTopic=${encodeURIComponent(fullTopic)}`)
    return response.data
  }

  // ==================== Topic数据管理 ====================

  /**
   * 查询Topic数据
   */
  static async getTopicData(params: GetTopicDataParams): Promise<ApiResponse<TopicDataResponse>> {
    const response = await apiClient.get('/groups/topic-data', { params })
    return response.data
  }

  /**
   * 删除Topic数据
   */
  static async deleteTopicData(params: DeleteTopicDataParams): Promise<ApiResponse<DeleteTopicDataResponse>> {
    const response = await apiClient.delete('/groups/topic-data', { params })
    return response.data
  }

  // ==================== 预警规则管理 ====================

  /**
   * 创建预警规则
   */
  static async createAlertRule(data: CreateAlertRuleRequest): Promise<ApiResponse<AlertRule>> {
    const response = await apiClient.post('/groups/alert-rules', data)
    return response.data
  }

  /**
   * 获取预警规则列表
   */
  static async getAlertRules(params?: GetAlertRulesParams): Promise<ApiResponse<PaginatedResponse<AlertRule>>> {
    const response = await apiClient.get('/groups/alert-rules', { params })
    return response.data
  }

  /**
   * 获取预警规则详情
   */
  static async getAlertRule(ruleId: string): Promise<ApiResponse<AlertRule>> {
    const response = await apiClient.get(`/groups/alert-rules/${ruleId}`)
    return response.data
  }

  /**
   * 更新预警规则
   */
  static async updateAlertRule(ruleId: string, data: UpdateAlertRuleRequest): Promise<ApiResponse<AlertRule>> {
    const response = await apiClient.put(`/groups/alert-rules/${ruleId}`, data)
    return response.data
  }

  /**
   * 删除预警规则
   */
  static async deleteAlertRule(ruleId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/groups/alert-rules/${ruleId}`)
    return response.data
  }

  /**
   * 测试预警规则
   */
  static async testAlertRule(ruleId: string, data: TestAlertRuleRequest): Promise<ApiResponse> {
    const response = await apiClient.post(`/groups/alert-rules/${ruleId}/test`, data)
    return response.data
  }

  // ==================== 预警记录管理 ====================

  /**
   * 获取预警记录列表
   */
  static async getAlertRecords(params?: GetAlertRecordsParams): Promise<ApiResponse<PaginatedResponse<AlertRecord>>> {
    const response = await apiClient.get('/groups/alert-records', { params })
    return response.data
  }


}

// React Query keys
export const groupsQueryKeys = {
  all: ['groups'] as const,
  lists: () => [...groupsQueryKeys.all, 'list'] as const,
  list: (params?: GetGroupsParams) => [...groupsQueryKeys.lists(), params] as const,
  myGroups: () => [...groupsQueryKeys.all, 'my'] as const,
  details: () => [...groupsQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...groupsQueryKeys.details(), id] as const,
  members: (groupId: string, params?: GetMembersParams) => [...groupsQueryKeys.all, 'members', groupId, params] as const,
  topics: (groupId: string) => [...groupsQueryKeys.all, 'topics', groupId] as const,
  permissions: (groupId: string, fullTopic: string, params?: GetPermissionsParams) =>
    [...groupsQueryKeys.all, 'permissions', groupId, fullTopic, params] as const,
  requests: (groupId: string, params?: GetRequestsParams) =>
    [...groupsQueryKeys.all, 'requests', groupId, params] as const,
  topicData: (params: GetTopicDataParams) =>
    [...groupsQueryKeys.all, 'topic-data', params] as const,
  alertRules: (params?: GetAlertRulesParams) =>
    [...groupsQueryKeys.all, 'alert-rules', params] as const,
  alertRule: (ruleId: string) =>
    [...groupsQueryKeys.all, 'alert-rule', ruleId] as const,
  alertRecords: (params?: GetAlertRecordsParams) =>
    [...groupsQueryKeys.all, 'alert-records', params] as const,
}

// 导出类型
export type {
  ApiResponse,
  PaginatedResponse,
  Group,
  GroupMember,
  GroupTopic,
  TopicPermission,
  JoinRequest,
  TopicMessage,
  TopicDataResponse,
  DeleteTopicDataResponse,
  AlertRule,
  AlertRecord,


  GroupDetail,
  CreateGroupRequest,
  UpdateGroupRequest,
  JoinGroupRequest,
  ReviewJoinRequest,
  CreateTopicRequest,
  SetPermissionRequest,
  CreateAlertRuleRequest,
  UpdateAlertRuleRequest,
  TestAlertRuleRequest,
  GetGroupsParams,
  GetMembersParams,
  GetRequestsParams,
  GetPermissionsParams,
  GetTopicDataParams,
  DeleteTopicDataParams,
  GetAlertRulesParams,
  GetAlertRecordsParams,
}
