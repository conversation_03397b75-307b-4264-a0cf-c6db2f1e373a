export const dataTableConfig = {
  // 默认分页配置
  pagination: {
    defaultPageSize: 10,
    pageSizeOptions: [10, 20, 30, 40, 50],
  },
  
  // 默认排序配置
  sorting: {
    defaultSort: null,
    multiSort: false,
  },
  
  // 默认筛选配置
  filtering: {
    debounceMs: 300,
    caseSensitive: false,
  },

  // 筛选器变体
  filterVariants: ['default', 'outline'] as const,

  // 筛选器操作符
  operators: ['eq', 'ne', 'contains', 'startsWith', 'endsWith'] as const,
  
  // 表格样式配置
  styling: {
    showBorder: true,
    showHeader: true,
    showPagination: true,
    density: 'normal' as 'compact' | 'normal' | 'comfortable',
  },
} as const

export type DataTableConfig = typeof dataTableConfig
