import { toast } from 'sonner'

export const CopyContent = async (content: string) => {
    const showSuccess = () => toast.success('👋 Copied to clipboard!')
    const showError = () => toast.error('Failed to copy. Please copy manually.')

    try {
        if (!content) throw new Error('❗️ Content is empty')

        // Use Clipboard API if available
        if (navigator.clipboard) {
            await navigator.clipboard.writeText(content)
            showSuccess()
            return
        }

        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = content
        textArea.setAttribute('readonly', '')
        textArea.style.position = 'absolute'
        textArea.style.left = '-9999px'
        document.body.appendChild(textArea)

        textArea.select()
        textArea.setSelectionRange(0, textArea.value.length)

        const success = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (success) {
            showSuccess()
        } else {
            throw new Error('☹️ execCommand copy failed')
        }
    } catch (err) {
        console.error('☹️ Copy failed:', err)
        showError()
    }
}
