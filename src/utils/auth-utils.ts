import { AuthUser } from '@/stores/authStore'

/**
 * 检查用户是否为管理员
 * @param user 用户信息
 * @returns 是否为管理员
 */
export function isAdmin(user: AuthUser | null): boolean {
  return user?.role === 'admin'
}

/**
 * 检查用户是否有管理员权限
 * @param user 用户信息
 * @returns 是否有管理员权限
 */
export function hasAdminPermission(user: AuthUser | null): boolean {
  return isAdmin(user)
}

/**
 * 检查用户是否可以访问管理员页面
 * @param user 用户信息
 * @returns 是否可以访问管理员页面
 */
export function canAccessAdminPages(user: AuthUser | null): boolean {
  return isAdmin(user)
}
