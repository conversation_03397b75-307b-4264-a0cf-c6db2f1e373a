import { useState, useEffect, useCallback, useRef } from 'react'

// 简单的查询选项
export interface QueryOptions<T> {
  enabled?: boolean
  select?: (data: any) => T
  onSuccess?: (data: T) => void
  onError?: (error: any) => void
}

// 查询结果
export interface QueryResult<T> {
  data: T | undefined
  isLoading: boolean
  isError: boolean
  error: any
  refetch: () => Promise<void>
  isSuccess: boolean
}

// 变更选项
export interface MutationOptions<TData, TVariables> {
  onSuccess?: (data: TData, variables: TVariables) => void
  onError?: (error: any, variables: TVariables) => void
  onSettled?: (data: TData | undefined, error: any, variables: TVariables) => void
}

// 变更结果
export interface MutationResult<TData, TVariables> {
  mutate: (variables: TVariables) => Promise<TData>
  mutateAsync: (variables: TVariables) => Promise<TData>
  isLoading: boolean
  isPending: boolean // 添加 isPending 属性以兼容 TanStack Query
  isError: boolean
  error: any
  data: TData | undefined
  reset: () => void
}

// 简化的 useQuery hook
export function useQuery<T = any>(
  options: { queryKey: readonly any[], queryFn: () => Promise<any> } & QueryOptions<T>
): QueryResult<T> {
  const {
    queryKey,
    queryFn,
    enabled = true,
    select,
    onSuccess,
    onError
  } = options

  const [data, setData] = useState<T | undefined>(undefined)
  const [isLoading, setIsLoading] = useState(false)
  const [isError, setIsError] = useState(false)
  const [error, setError] = useState<any>(null)
  const [isSuccess, setIsSuccess] = useState(false)

  // 使用 ref 来存储最新的函数，避免依赖问题
  const queryFnRef = useRef(queryFn)
  const selectRef = useRef(select)
  const onSuccessRef = useRef(onSuccess)
  const onErrorRef = useRef(onError)

  // 更新 refs
  queryFnRef.current = queryFn
  selectRef.current = select
  onSuccessRef.current = onSuccess
  onErrorRef.current = onError

  const refetch = useCallback(async () => {
    if (!enabled) return

    setIsLoading(true)
    setIsError(false)
    setError(null)

    try {
      const result = await queryFnRef.current()
      const finalData = selectRef.current ? selectRef.current(result) : result

      setData(finalData)
      setIsSuccess(true)
      setIsError(false)
      setError(null)
      onSuccessRef.current?.(finalData)
    } catch (err) {
      setIsError(true)
      setError(err)
      setIsSuccess(false)
      onErrorRef.current?.(err)
    } finally {
      setIsLoading(false)
    }
  }, [enabled])

  // 当 queryKey 或 enabled 变化时重新获取数据
  useEffect(() => {
    if (enabled) {
      refetch()
    }
  }, [JSON.stringify(queryKey), enabled, refetch]) // 依赖 queryKey 的序列化版本

  return {
    data,
    isLoading,
    isError,
    error,
    refetch,
    isSuccess
  }
}

// 自定义 useMutation hook - 支持两种调用方式
export function useMutation<TData = any, TVariables = any>(
  mutationFnOrOptions: ((variables: TVariables) => Promise<TData>) | ({ mutationFn: (variables: TVariables) => Promise<TData> } & MutationOptions<TData, TVariables>),
  options: MutationOptions<TData, TVariables> = {}
): MutationResult<TData, TVariables> {
  // 处理两种调用方式
  let mutationFn: (variables: TVariables) => Promise<TData>
  let finalOptions: MutationOptions<TData, TVariables>

  if (typeof mutationFnOrOptions === 'function') {
    // 旧的调用方式: useMutation(mutationFn, options)
    mutationFn = mutationFnOrOptions
    finalOptions = options
  } else {
    // 新的调用方式: useMutation({ mutationFn, ...options })
    mutationFn = mutationFnOrOptions.mutationFn
    finalOptions = mutationFnOrOptions
  }
  const { onSuccess, onError, onSettled } = finalOptions

  const [data, setData] = useState<TData | undefined>(undefined)
  const [isLoading, setIsLoading] = useState(false)
  const [isError, setIsError] = useState(false)
  const [error, setError] = useState<any>(null)

  const mutate = useCallback(async (variables: TVariables): Promise<TData> => {
    setIsLoading(true)
    setIsError(false)
    setError(null)

    try {
      const result = await mutationFn(variables)
      setData(result)
      setIsError(false)
      setError(null)
      onSuccess?.(result, variables)
      onSettled?.(result, null, variables)
      return result
    } catch (err) {
      setIsError(true)
      setError(err)
      onError?.(err, variables)
      onSettled?.(undefined, err, variables)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [mutationFn, onSuccess, onError, onSettled])

  const reset = useCallback(() => {
    setData(undefined)
    setIsLoading(false)
    setIsError(false)
    setError(null)
  }, [])

  return {
    mutate,
    mutateAsync: mutate,
    isLoading,
    isPending: isLoading, // 添加 isPending 以兼容 TanStack Query
    isError,
    error,
    data,
    reset
  }
}

// 简化的缓存管理工具（无实际缓存功能）
export const queryCache = {
  invalidateQueries: (options: { queryKey: readonly any[] }) => {
    // 简化版本，不做实际缓存操作
    console.log('Invalidating queries:', options.queryKey)
  },

  invalidateAll: () => {
    // 简化版本，不做实际缓存操作
    console.log('Invalidating all queries')
  },

  setQueryData: <T>(queryKey: readonly any[], data: T) => {
    // 简化版本，不做实际缓存操作
    console.log('Setting query data:', queryKey, data)
  },

  getQueryData: <T>(queryKey: readonly any[]): T | undefined => {
    // 简化版本，不做实际缓存操作
    console.log('Getting query data:', queryKey)
    return undefined
  }
}

// 模拟 useQueryClient
export function useQueryClient() {
  return queryCache
}
