import React, { useState, createContext, useContext } from 'react'
import useDialogState from './use-dialog-state'

// 通用对话框管理器接口
export interface DialogManagerContextType<TDialogType extends string, TRowType = any> {
  open: TDialogType | null
  setOpen: (str: TDialogType | null) => void
  currentRow: TRowType | null
  setCurrentRow: React.Dispatch<React.SetStateAction<TRowType | null>>
  openDialog: (dialogType: TDialogType, row?: TRowType) => void
  closeDialog: (delay?: number) => void
}

// 创建通用的对话框管理器hook
export function createDialogManager<TDialogType extends string, TRowType = any>() {
  const DialogManagerContext = createContext<DialogManagerContextType<TDialogType, TRowType> | null>(null)

  // Provider组件
  function DialogManagerProvider({ children }: { children: React.ReactNode }) {
    const [open, setOpen] = useDialogState<TDialogType>(null)
    const [currentRow, setCurrentRow] = useState<TRowType | null>(null)

    const openDialog = (dialogType: TDialogType, row?: TRowType) => {
      if (row) {
        setCurrentRow(row)
      }
      setOpen(dialogType)
    }

    const closeDialog = (delay: number = 500) => {
      setOpen(null)
      setTimeout(() => {
        setCurrentRow(null)
      }, delay)
    }

    const contextValue: DialogManagerContextType<TDialogType, TRowType> = {
      open,
      setOpen,
      currentRow,
      setCurrentRow,
      openDialog,
      closeDialog,
    }

    return (
      <DialogManagerContext.Provider value={contextValue}>
        {children}
      </DialogManagerContext.Provider>
    )
  }

  // Hook
  function useDialogManager() {
    const context = useContext(DialogManagerContext)
    if (!context) {
      throw new Error('useDialogManager must be used within a DialogManagerProvider')
    }
    return context
  }

  return {
    DialogManagerProvider,
    useDialogManager,
  }
}

// 通用对话框配置接口
export interface DialogConfig<TDialogType extends string, TRowType = any> {
  key: string
  type: TDialogType
  component: React.ComponentType<{
    open: boolean
    onOpenChange: (open: boolean) => void
    currentRow?: TRowType
  }>
  requiresRow?: boolean
  onClose?: (currentRow?: TRowType) => void
}

// 通用对话框渲染器组件
export interface DialogRendererProps<TDialogType extends string, TRowType = any> {
  dialogs: DialogConfig<TDialogType, TRowType>[]
  open: TDialogType | null
  setOpen: (type: TDialogType | null) => void
  currentRow: TRowType | null
  setCurrentRow: React.Dispatch<React.SetStateAction<TRowType | null>>
}

export function DialogRenderer<TDialogType extends string, TRowType = any>({
  dialogs,
  open,
  setOpen,
  currentRow,
  setCurrentRow,
}: DialogRendererProps<TDialogType, TRowType>) {
  const handleDialogClose = (dialogConfig: DialogConfig<TDialogType, TRowType>) => {
    setOpen(null)
    if (dialogConfig.onClose) {
      dialogConfig.onClose(currentRow || undefined)
    }
    setTimeout(() => {
      setCurrentRow(null)
    }, 500)
  }

  return (
    <>
      {dialogs.map((dialogConfig) => {
        const DialogComponent = dialogConfig.component
        const isOpen = open === dialogConfig.type
        const shouldRender = dialogConfig.requiresRow ? currentRow && isOpen : isOpen

        if (!shouldRender) return null

        return (
          <DialogComponent
            key={`${dialogConfig.key}-${(currentRow as any)?.id || 'no-row'}`}
            open={isOpen}
            onOpenChange={() => handleDialogClose(dialogConfig)}
            currentRow={currentRow || undefined}
          />
        )
      })}
    </>
  )
}
