import { createBrowserRouter, Navigate } from 'react-router-dom'
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout'
import { NavigationProgress } from '@/components/custom_components/navigation-progress'
import { useAuthStore } from '@/stores/authStore'
import { canAccessAdminPages } from '@/utils/auth-utils'

// 错误页面
import GeneralError from '@/features/errors/general-error'
import NotFoundError from '@/features/errors/not-found-error'

// 认证页面
import SignIn from '@/features/auth/sign-in/sign-in'
import SignUp from '@/features/auth/sign-up'
import ForgotPassword from '@/features/auth/forgot-password'

// 主要页面
import Dashboard from '@/features/dashboard'
import Settings from '@/features/settings'
import SettingsProfile from '@/features/settings/profile'
import SettingsAccount from '@/features/settings/account'
import SettingsAppearance from '@/features/settings/appearance'



// 管理页面
import AdminUsersPage from '@/features/admin/users'
import AdminLogManagePage from '@/features/admin/logManage'
import { MonitorPage } from '@/features/admin/monitor/monitor-page'
import AdminDevicesPage from '@/features/admin/devices'
import AdminDeviceTypesPage from '@/features/admin/devices/device-types'

// MQTT 页面
import GroupsPage from '@/features/groups/groups-page'
import GroupDetailPage from '@/features/groups/group-detail'

// 帮助中心
import HelpCenterPage from '@/features/help-center'


//error-pages
import Error401 from "@/features/errors/unauthorized-error"
import Error403 from "@/features/errors/forbidden"
import Error404 from "@/features/errors/not-found-error"
import Error500 from "@/features/errors/general-error"
import Error503 from "@/features/errors/maintenance-error"

// 认证守卫组件
function AuthGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, accessToken } = useAuthStore()

  if (!isAuthenticated || !accessToken) {
    return <Navigate to="/sign-in" replace />
  }

  return <>{children}</>
}

// 管理员权限守卫组件
function AdminGuard({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated, accessToken } = useAuthStore()

  if (!isAuthenticated || !accessToken) {
    return <Navigate to="/sign-in" replace />
  }

  if (!canAccessAdminPages(user)) {
    return <Navigate to="/401" replace />
  }

  return <>{children}</>
}

// 根布局组件
function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <NavigationProgress />
      {children}
      {/* 这里可以添加全局组件，如 Toaster */}
    </>
  )
}

// 创建路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout><Navigate to="/dashboard" replace /></RootLayout>,
    errorElement: <GeneralError />
  },
  
  // 认证路由
  {
    path: '/sign-in',
    element: <RootLayout><SignIn /></RootLayout>
  },
  {
    path: '/sign-up', 
    element: <RootLayout><SignUp /></RootLayout>
  },
  {
    path: '/forgot-password',
    element: <RootLayout><ForgotPassword /></RootLayout>
  },
  
  // 错误页面路由
  {
    path: '/401',
    element: <RootLayout><Error401 /></RootLayout>
  },
  {
    path: '/403',
    element: <RootLayout><Error403 /></RootLayout>
  },
  {
    path: '/404',
    element: <RootLayout><Error404 /></RootLayout>
  },
  {
    path: '/500',
    element: <RootLayout><Error500 /></RootLayout>
  },
  {
    path: '/503',
    element: <RootLayout><Error503 /></RootLayout>
  },
  
  // 认证后的路由
  {
    path: '/dashboard',
    element: (
      <RootLayout>
        <AuthGuard>
          <AuthenticatedLayout>
            <Dashboard />
          </AuthenticatedLayout>
        </AuthGuard>
      </RootLayout>
    )
  },

  
  // 设置页面
  {
    path: '/settings',
    element: (
      <RootLayout>
        <AuthGuard>
          <AuthenticatedLayout>
            <Settings />
          </AuthenticatedLayout>
        </AuthGuard>
      </RootLayout>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/settings/profile" replace />
      },
      {
        path: 'profile',
        element: <SettingsProfile />
      },
      {
        path: 'account',
        element: <SettingsAccount />
      },
      {
        path: 'appearance',
        element: <SettingsAppearance />
      },
    ]
  },
  
  // 管理页面
  {
    path: '/admin/users',
    element: (
      <RootLayout>
        <AdminGuard>
          <AuthenticatedLayout>
            <AdminUsersPage />
          </AuthenticatedLayout>
        </AdminGuard>
      </RootLayout>
    )
  },
  {
    path: '/admin/logs',
    element: (
      <RootLayout>
        <AdminGuard>
          <AuthenticatedLayout>
            <AdminLogManagePage />
          </AuthenticatedLayout>
        </AdminGuard>
      </RootLayout>
    )
  },
  {
    path: '/admin/devices',
    element: (
      <RootLayout>
        <AdminGuard>
          <AuthenticatedLayout>
            <AdminDevicesPage />
          </AuthenticatedLayout>
        </AdminGuard>
      </RootLayout>
    )
  },
  {
    path: '/admin/device-types',
    element: (
      <RootLayout>
        <AdminGuard>
          <AuthenticatedLayout>
            <AdminDeviceTypesPage />
          </AuthenticatedLayout>
        </AdminGuard>
      </RootLayout>
    )
  },
  
  // MQTT 页面
  {
    path: '/mqtt/groups',
    element: (
      <RootLayout>
        <AuthGuard>
          <AuthenticatedLayout>
            <GroupsPage />
          </AuthenticatedLayout>
        </AuthGuard>
      </RootLayout>
    )
  },
  {
    path: '/mqtt/groups/:groupId',
    element: (
      <RootLayout>
        <AuthGuard>
          <AuthenticatedLayout>
            <GroupDetailPage />
          </AuthenticatedLayout>
        </AuthGuard>
      </RootLayout>
    )
  },
  
  // 监控页面
  {
    path: '/admin/monitor',
    element: (
      <RootLayout>
        <AdminGuard>
          <AuthenticatedLayout>
            <MonitorPage />
          </AuthenticatedLayout>
        </AdminGuard>
      </RootLayout>
    )
  },
  
  // 帮助中心
  {
    path: '/help-center',
    element: (
      <RootLayout>
        <AuthGuard>
          <AuthenticatedLayout>
            <HelpCenterPage />
          </AuthenticatedLayout>
        </AuthGuard>
      </RootLayout>
    )
  },
  
  // 404 页面 - 必须放在最后
  {
    path: '*',
    element: <RootLayout><NotFoundError /></RootLayout>
  }
])
