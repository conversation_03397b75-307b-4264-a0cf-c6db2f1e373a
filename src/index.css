@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.21 0.03 263.61);
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.21 0.03 263.61);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.21 0.03 263.61);
  --primary: oklch(0.48 0.20 260.47);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.37 0.03 259.73);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.55 0.02 264.41);
  --accent: oklch(0.95 0.02 260.18);
  --accent-foreground: oklch(0.48 0.20 260.47);
  --destructive: oklch(0.58 0.22 27.29);
  --border: oklch(0.93 0.01 261.82);
  --input: oklch(0.93 0.01 261.82);
  --ring: oklch(0.48 0.20 260.47);
  --chart-1: oklch(0.48 0.20 260.47);
  --chart-2: oklch(0.56 0.24 260.92);
  --chart-3: oklch(0.40 0.16 259.61);
  --chart-4: oklch(0.43 0.16 259.82);
  --chart-5: oklch(0.29 0.07 261.20);
  --sidebar: oklch(0.97 0 0);
  --sidebar-foreground: oklch(0.21 0.03 263.61);
  --sidebar-primary: oklch(0.48 0.20 260.47);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.95 0.02 260.18);
  --sidebar-accent-foreground: oklch(0.48 0.20 260.47);
  --sidebar-border: oklch(0.93 0.01 261.82);
  --sidebar-ring: oklch(0.48 0.20 260.47);

  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: IBM Plex Mono, monospace;

  --radius: 0.375rem;

  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);

}

.dark {
  --background: oklch(0.26 0.03 262.67);
  --foreground: oklch(0.93 0.01 261.82);
  --card: oklch(0.30 0.03 260.51);
  --card-foreground: oklch(0.93 0.01 261.82);
  --popover: oklch(0.30 0.03 260.51);
  --popover-foreground: oklch(0.93 0.01 261.82);
  --primary: oklch(0.56 0.24 260.92);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.35 0.04 261.40);
  --secondary-foreground: oklch(0.93 0.01 261.82);
  --muted: oklch(0.30 0.03 260.51);
  --muted-foreground: oklch(0.71 0.02 261.33);
  --accent: oklch(0.33 0.04 264.63);
  --accent-foreground: oklch(0.93 0.01 261.82);
  --destructive: oklch(0.64 0.21 25.39);
  --border: oklch(0.35 0.04 261.40);
  --input: oklch(0.35 0.04 261.40);
  --ring: oklch(0.56 0.24 260.92);
  --chart-1: oklch(0.56 0.24 260.92);
  --chart-2: oklch(0.48 0.20 260.47);
  --chart-3: oklch(0.69 0.17 255.59);
  --chart-4: oklch(0.43 0.16 259.82);
  --chart-5: oklch(0.29 0.07 261.20);
  --sidebar: oklch(0.26 0.03 262.67);
  --sidebar-foreground: oklch(0.93 0.01 261.82);
  --sidebar-primary: oklch(0.56 0.24 260.92);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.33 0.04 264.63);
  --sidebar-accent-foreground: oklch(0.93 0.01 261.82);
  --sidebar-border: oklch(0.35 0.04 261.40);
  --sidebar-ring: oklch(0.56 0.24 260.92);

  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --font-inter: 'Inter', 'sans-serif';
    --font-manrope: 'Manrope', 'sans-serif';
    --font-roboto: 'Roboto', 'sans-serif';
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 通用滚动条样式（支持明暗主题） */
::-webkit-scrollbar {
  width: 0.75rem;
  height: 0.75rem;
  background-color: var(--muted);
}

::-webkit-scrollbar-track {
  background-color: var(--muted);
  border-radius: 0.625rem;
  box-shadow: inset 0 0 0.375rem var(--border);
}

::-webkit-scrollbar-thumb {
  background-color: var(--accent);
  border-radius: 0.625rem;
  box-shadow: inset 0 0 0.375rem var(--muted-foreground);
}

/* Firefox support */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--accent) var(--muted);
}