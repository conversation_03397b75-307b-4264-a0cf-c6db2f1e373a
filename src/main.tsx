import { StrictMode } from 'react'
import ReactD<PERSON> from 'react-dom/client'
import { RouterProvider } from 'react-router-dom'
import { FontProvider } from './context/font-context'
import { ThemeProvider } from './context/theme-context'
import { Toaster } from '@/components/ui/sonner'
import { router } from './router'
import './index.css'
// i18n
import './i18n'

// Initialize auth store
import { useAuthStore } from '@/stores/authStore'
useAuthStore.getState().initialize()

// Render the app
const rootElement = document.getElementById('root')!
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <ThemeProvider defaultTheme='light' storageKey='vite-ui-theme'>
        <FontProvider>
          <RouterProvider router={router} />
          <Toaster duration={3000} />
        </FontProvider>
      </ThemeProvider>
    </StrictMode>
  )
}
