import { useState } from 'react'
import { useQuery } from '@/hooks/use-api'
import { useParams, Link } from 'react-router-dom'
import { ArrowLeft, Users, MessageSquare, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import { useAuthStore } from '@/stores/authStore'
import { GroupOverview } from './components/group-overview'
import { MemberManagement } from './components/member-management'
import { TopicManagement } from './components/topic-management'

export default function GroupDetailPage() {
  const { groupId } = useParams<{ groupId: string }>()
  const [activeTab, setActiveTab] = useState('overview')

  // 从 zustand 获取当前用户信息
  const { user } = useAuthStore()

  // 获取分组详情
  const { data: group, isLoading, error } = useQuery({
    queryKey: groupsQueryKeys.detail(groupId || ''),
    queryFn: async () => {
      if (!groupId) throw new Error('Group ID is required')
      const response = await GroupsAPI.getGroup(groupId)
      return response.data
    },
    enabled: !!groupId,
  })

  // 计算是否为创建者
  const isCreator = group && user ? group.creator_id === user.id : false

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃'
      case 'inactive':
        return '非活跃'
      case 'archived':
        return '已归档'
      default:
        return status
    }
  }

  const breadcrumbItems = [
    { title: '首页', href: '/' },
    { title: 'MQTT', href: '/mqtt' },
    { title: '分组管理', href: '/mqtt/groups' },
    { title: group?.name || '分组详情', isActive: true }
  ]

  if (isLoading) {
    return (
      <DashboardLayout
        breadcrumbItems={[
          { title: '首页', href: '/' },
          { title: 'MQTT', href: '/mqtt' },
          { title: '分组管理', href: '/mqtt/groups' },
          { title: '分组详情', isActive: true }
        ]}
        pageHeader={{
          title: '分组详情',
          description: '加载中...'
        }}
      >
        <Card>
          <CardContent className="pt-4 pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
              <Skeleton className="h-3 w-24" />
            </div>
          </CardContent>
        </Card>
        <div className="mt-4">
          <Skeleton className="h-9 w-full mb-4" />
          <Skeleton className="h-32 w-full" />
        </div>
      </DashboardLayout>
    )
  }

  if (error || !group) {
    return (
      <DashboardLayout
        breadcrumbItems={[
          { title: '首页', href: '/' },
          { title: 'MQTT', href: '/mqtt' },
          { title: '分组管理', href: '/mqtt/groups' },
          { title: '分组详情', isActive: true }
        ]}
        pageHeader={{
          title: '分组不存在',
          description: '请检查分组ID是否正确，或者您是否有访问权限'
        }}
      >
        <div className="text-center py-12">
          <Link to="/mqtt/groups">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回分组列表
            </Button>
          </Link>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      breadcrumbItems={breadcrumbItems}
      pageHeader={{
        title: group.name,
        description: group.description,
        actions: (
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(group.status)} variant="outline">
              {getStatusText(group.status)}
            </Badge>
            <Link to="/mqtt/groups">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-3 h-3 mr-1" />
                返回
              </Button>
            </Link>
          </div>
        )
      }}
    >

      {/* 分组信息条 - 一行展示 */}
      <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg mb-6 text-sm">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-1.5">
            <Users className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">{group.member_count}</span>
            <span className="text-muted-foreground">/ {group.max_members} 成员</span>
          </div>
          <div className="flex items-center gap-1.5">
            <MessageSquare className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">{group.topic_count}</span>
            <span className="text-muted-foreground">Topics</span>
          </div>
          <div className="flex items-center gap-1.5">
            <Settings className="w-4 h-4 text-muted-foreground" />
            <span className="text-muted-foreground">创建于 {new Date(group.created_at).toLocaleDateString()}</span>
          </div>
        </div>
        <div className="text-xs text-muted-foreground font-mono bg-background px-2 py-1 rounded border">
          {group.group_id}
        </div>
      </div>

      {/* 管理选项卡 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 h-10">
          <TabsTrigger value="overview" className="flex items-center gap-2 text-sm">
            <Settings className="w-4 h-4" />
            概览
          </TabsTrigger>
          <TabsTrigger value="members" className="flex items-center gap-2 text-sm">
            <Users className="w-4 h-4" />
            成员
          </TabsTrigger>
          <TabsTrigger value="topics" className="flex items-center gap-2 text-sm">
            <MessageSquare className="w-4 h-4" />
            Topics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-0">
          <GroupOverview group={group} />
        </TabsContent>

        <TabsContent value="members" className="space-y-0">
          <MemberManagement groupId={group.group_id} isCreator={isCreator} />
        </TabsContent>

        <TabsContent value="topics" className="space-y-0">
          <TopicManagement groupId={group.group_id} isCreator={isCreator} />
        </TabsContent>
      </Tabs>
    </DashboardLayout>
  )
}
