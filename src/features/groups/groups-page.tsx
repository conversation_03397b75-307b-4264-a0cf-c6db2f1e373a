import { useState } from 'react'
import { useQuery } from '@/hooks/use-api'
import { Plus, Search, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import { GroupCard } from './components/group-card'
import { CreateGroupDialog } from './components/create-group-dialog'
import { JoinGroupRequestDialog } from './components/join-group-request-dialog'
import { RequestsNotification } from './components/requests-notification'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export default function GroupsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'archived'>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showJoinDialog, setShowJoinDialog] = useState(false)

  // 注意：由于只能获取用户自己的分组，暂时不需要查询参数

  // 获取我的分组
  const { data: myGroupsResponse, isLoading: myGroupsLoading } = useQuery({
    queryKey: groupsQueryKeys.myGroups(),
    queryFn: async () => {
      const response = await GroupsAPI.getMyGroups()
      return response.data
    },
  })

  const myGroups = myGroupsResponse || []

  // 注意：API文档中没有统计接口，我们从现有数据计算统计信息



  // 本地搜索和筛选
  const filteredGroups = myGroups.filter((group) => {
    const matchesSearch = searchTerm === '' ||
      group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || group.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const breadcrumbItems = [
    { title: '首页', href: '/' },
    { title: 'MQTT', href: '/mqtt' },
    { title: '分组管理', isActive: true },
  ]

  return (
    <DashboardLayout
      breadcrumbItems={breadcrumbItems}
      pageHeader={{
        title: '分组管理',
        description: '管理您的MQTT分组、成员和Topic权限',
        actions: (
          <div className="flex items-center gap-2">
            <RequestsNotification />
            <Button
              variant="outline"
              onClick={() => setShowJoinDialog(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              加入分组
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              创建分组
            </Button>
          </div>
        )
      }}
    >

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="pt-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索我的分组..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <Select
                value={statusFilter}
                onValueChange={(value) => setStatusFilter(value as 'all' | 'active' | 'inactive' | 'archived')}
              >
                <SelectTrigger className="border rounded-md px-3 py-2 text-sm" >
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">活跃</SelectItem>
                  <SelectItem value="inactive">非活跃</SelectItem>
                  <SelectItem value="archived">已归档</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 分组列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {myGroupsLoading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-16 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))
        ) : filteredGroups.length > 0 ? (
          filteredGroups.map((group) => (
            <GroupCard key={group.id} group={group} />
          ))
        ) : myGroups.length > 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500 mb-4">没有找到符合条件的分组</p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('')
                setStatusFilter('all')
              }}
            >
              清除筛选条件
            </Button>
          </div>
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500 mb-4">您还没有创建或加入任何分组</p>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              创建第一个分组
            </Button>
          </div>
        )}
      </div>

      {/* 创建分组对话框 */}
      <CreateGroupDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />

      {/* 加入分组对话框 */}
      <JoinGroupRequestDialog
        open={showJoinDialog}
        onOpenChange={setShowJoinDialog}
      />
    </DashboardLayout>
  )
}
