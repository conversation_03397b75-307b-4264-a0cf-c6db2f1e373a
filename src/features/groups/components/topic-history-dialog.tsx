import { useState, useEffect } from 'react'
import { useQuery } from '@/hooks/use-api'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  History,
  Clock,
  Download,
  RefreshCw,
  Search,
  X,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { DataTablePagination } from '@/components/ui/data-table-pagination'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { GroupTopic, TopicMessage, GetTopicDataParams } from '@/types/groups'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

interface TopicHistoryDialogProps {
  topic: GroupTopic | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function TopicHistoryDialog({ topic, open, onOpenChange }: TopicHistoryDialogProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [timeRange, setTimeRange] = useState('24h')
  const [searchTerm, setSearchTerm] = useState('')

  // 构建查询参数
  const queryParams: GetTopicDataParams = {
    topic: topic?.full_name || '',
    page: currentPage,
    pagesize: pageSize,
    timerange: timeRange
  }

  // 获取Topic历史数据
  const { data: historyResponse, isLoading, refetch, error } = useQuery({
    queryKey: groupsQueryKeys.topicData(queryParams),
    queryFn: async () => {
      if (!topic?.full_name) return null
      const response = await GroupsAPI.getTopicData(queryParams)
      return response.data
    },
    enabled: open && !!topic?.full_name,
  })

  const messages = historyResponse?.messages || []
  const total = historyResponse?.total || 0
  const totalPages = Math.ceil(total / pageSize)

  // 重置状态当对话框关闭时
  useEffect(() => {
    if (!open) {
      setCurrentPage(1)
      setSearchTerm('')
    }
  }, [open])

  // 过滤消息
  const filteredMessages = messages.filter((message: TopicMessage) => {
    if (!searchTerm) return true
    const searchLower = searchTerm.toLowerCase()
    return (
      message.payload.toLowerCase().includes(searchLower) ||
      message.client_id.toLowerCase().includes(searchLower)
    )
  })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRefresh = () => {
    refetch()
    toast.success('数据已刷新')
  }

  const handleExport = () => {
    if (filteredMessages.length === 0) {
      toast.error('没有数据可导出')
      return
    }

    const csvContent = [
      ['时间戳', 'Topic', '消息内容', 'QoS', '客户端ID'].join(','),
      ...filteredMessages.map((message: TopicMessage) => [
        message.timestamp,
        message.topic,
        `"${message.payload.replace(/"/g, '""')}"`, // 转义CSV中的引号
        message.qos,
        message.client_id
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `topic-history-${topic?.topic_name}-${Date.now()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    toast.success('数据导出成功')
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
    } catch {
      return timestamp
    }
  }

  const formatPayload = (payload: string) => {
    try {
      const parsed = JSON.parse(payload)
      return JSON.stringify(parsed, null, 2)
    } catch {
      return payload
    }
  }

  const getQoSBadgeVariant = (qos: number) => {
    switch (qos) {
      case 0: return 'secondary'
      case 1: return 'default'
      case 2: return 'destructive'
      default: return 'outline'
    }
  }

  if (!topic) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Topic历史记录
          </DialogTitle>
          <DialogDescription>
            查看Topic "{topic.topic_name}" 的历史消息数据
          </DialogDescription>
        </DialogHeader>

        {/* 控制栏 */}
        <div className="flex flex-col gap-4 border-b pb-4">
          {/* 第一行：时间范围和操作按钮 */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">最近1小时</SelectItem>
                  <SelectItem value="6h">最近6小时</SelectItem>
                  <SelectItem value="24h">最近1天</SelectItem>
                  <SelectItem value="168h">最近7天</SelectItem>
                  <SelectItem value="720h">最近30天</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10条</SelectItem>
                  <SelectItem value="20">20条</SelectItem>
                  <SelectItem value="50">50条</SelectItem>
                  <SelectItem value="100">100条</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button variant="outline" size="sm" onClick={handleExport} disabled={filteredMessages.length === 0}>
                <Download className="h-4 w-4" />
                导出
              </Button>
            </div>
          </div>

          {/* 第二行：搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索消息内容或客户端ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* 数据表格 */}
        <div className="flex-1 min-h-0">
          {error ? (
            <div className="flex items-center justify-center h-32 text-red-500">
              <p>加载数据失败: {error.message}</p>
            </div>
          ) : isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center gap-4 p-4 border rounded">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 flex-1" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          ) : filteredMessages.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-gray-500">
              <div className="text-center">
                <History className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>{searchTerm ? '没有找到匹配的消息' : '暂无历史数据'}</p>
              </div>
            </div>
          ) : (
            <ScrollArea className="h-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-40">时间戳</TableHead>
                    <TableHead className="w-20">QoS</TableHead>
                    <TableHead className="w-32">客户端ID</TableHead>
                    <TableHead>消息内容</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMessages.map((message: TopicMessage, index: number) => (
                    <TableRow key={`${message.timestamp}-${index}`}>
                      <TableCell className="font-mono text-xs">
                        {formatTimestamp(message.timestamp)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getQoSBadgeVariant(message.qos)}>
                          {message.qos}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {message.client_id}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="max-w-50 truncate cursor-default">
                              <div className="text-sm truncate">
                                {message.payload}
                              </div>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-sm whitespace-pre-wrap text-left text-xs font-mono">
                            {formatPayload(message.payload)}
                          </TooltipContent>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </div>

        {/* 分页 */}
        {!isLoading && !error && total > 0 && (
          <div className="border-t pt-4">
            <DataTablePagination
              mode="server"
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={total}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              isLoading={isLoading}
            />
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
