import { useState } from 'react'
import { useQ<PERSON>y, useMutation, useQueryClient } from '@/hooks/use-api'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  MessageSquare,
  Plus,
  Trash2,
  Copy,
  Search,
  Shield,
  History,
  MoreVertical,
  AlertTriangle,
  BarChart3,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import { TopicPermissionsDialog } from './topic-permissions-dialog'
import { TopicHistoryDialog } from './topic-history-dialog'
import { AlertRulesDialog } from './alert-rules-dialog'
import { AlertRecordsDialog } from './alert-records-dialog'
import type { GroupTopic, CreateTopicFormData } from '@/types/groups'
import { CopyContent } from '@/utils/utils'

const createTopicSchema = z.object({
  topic_name: z.string()
    .min(1, 'Topic名称不能为空')
    .max(50, 'Topic名称不能超过50个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Topic名称只能包含字母、数字、下划线和连字符'),
})

interface TopicManagementProps {
  groupId: string
  isCreator?: boolean
}

export function TopicManagement({ groupId, isCreator = false }: TopicManagementProps) {
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [deletingTopic, setDeletingTopic] = useState<GroupTopic | null>(null)
  const [permissionsTopic, setPermissionsTopic] = useState<GroupTopic | null>(null)
  const [historyTopic, setHistoryTopic] = useState<GroupTopic | null>(null)
  const [alertRulesTopic, setAlertRulesTopic] = useState<GroupTopic | null>(null)
  const [alertRecordsTopic, setAlertRecordsTopic] = useState<GroupTopic | null>(null)

  const form = useForm<CreateTopicFormData>({
    resolver: zodResolver(createTopicSchema),
    defaultValues: {
      topic_name: '',
    },
  })

  // 获取Topic列表
  const { data: topicsResponse, isLoading, refetch: refetchTopics } = useQuery({
    queryKey: groupsQueryKeys.topics(groupId),
    queryFn: async () => {
      const response = await GroupsAPI.getTopics(groupId)
      return response.data
    },
  })

  const topics = topicsResponse?.topics || []

  // 创建Topic
  const createTopicMutation = useMutation({
    mutationFn: (data: CreateTopicFormData) => GroupsAPI.createTopic(groupId, data),
    onSuccess: async () => {
      toast.success('Topic创建成功')
      // 立即重新获取 topic 信息
      await refetchTopics()
      // 同时使相关查询失效，确保其他组件也能获取到最新数据
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.topics(groupId) })
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.detail(groupId) })
      form.reset()
      setShowCreateDialog(false)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '创建Topic失败')
    },
  })

  // 删除Topic
  const deleteTopicMutation = useMutation({
    mutationFn: (topicId: string) => GroupsAPI.deleteTopic(groupId, topicId),
    onSuccess: async () => {
      toast.success('Topic删除成功')
      // 立即重新获取 topic 信息
      await refetchTopics()
      // 同时使相关查询失效，确保其他组件也能获取到最新数据
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.topics(groupId) })
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.detail(groupId) })
      setDeletingTopic(null)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '删除Topic失败')
    },
  })

  const handleCreateTopic = (data: CreateTopicFormData) => {
    createTopicMutation.mutate(data)
  }

  const handleDeleteTopic = (topic: GroupTopic) => {
    setDeletingTopic(topic)
  }

  const confirmDeleteTopic = () => {
    if (deletingTopic) {
      deleteTopicMutation.mutate(deletingTopic.id)
    }
  }


  // 过滤Topic
  const filteredTopics = topics?.filter((topic: GroupTopic) =>
    topic.topic_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    topic.full_name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
                <Skeleton className="h-8 w-8 rounded" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
                <Skeleton className="h-8 w-8 ml-auto" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Topic列表 ({topics?.length || 0})
            </CardTitle>
            {isCreator && (
              <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    创建Topic
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>创建新Topic</DialogTitle>
                    <DialogDescription>
                      在分组内创建一个新的MQTT Topic。Topic名称将自动添加分组ID前缀。
                    </DialogDescription>
                  </DialogHeader>

                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleCreateTopic)} className="space-y-4">
                      <FormField
                        control={form.control}
                        name="topic_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Topic名称</FormLabel>
                            <FormControl>
                              <Input placeholder="temperature" {...field} />
                            </FormControl>
                            <FormDescription>
                              完整Topic名称将为：{groupId}/{field.value || 'topic_name'}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <DialogFooter>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setShowCreateDialog(false)}
                        >
                          取消
                        </Button>
                        <Button
                          type="submit"
                          disabled={createTopicMutation.isPending}
                        >
                          {createTopicMutation.isPending ? '创建中...' : '创建Topic'}
                        </Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索Topic..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardHeader>

        <CardContent>
          {filteredTopics.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500 mb-4">
                {searchTerm ? '没有找到匹配的Topic' : '暂无Topic'}
              </p>
              {isCreator && !searchTerm && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建第一个Topic
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {filteredTopics.map((topic: GroupTopic) => (
                <Card key={topic.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1 min-w-0">
                        {/* Topic图标 */}
                        <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <MessageSquare className="h-5 w-5 text-primary" />
                        </div>

                        {/* Topic信息 */}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-sm truncate mb-1">
                            {topic.topic_name}
                          </h3>
                          <p className="text-xs text-muted-foreground truncate mb-2">
                            {topic.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(topic.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      {/* 操作菜单 */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => CopyContent(topic.full_name)}>
                            <Copy className="mr-2 h-4 w-4" />
                            复制名称
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setHistoryTopic(topic)}>
                            <History className="mr-2 h-4 w-4" />
                            历史数据
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setPermissionsTopic(topic)}>
                            <Shield className="mr-2 h-4 w-4" />
                            权限管理
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setAlertRulesTopic(topic)}>
                            <AlertTriangle className="mr-2 h-4 w-4" />
                            预警规则
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setAlertRecordsTopic(topic)}>
                            <BarChart3 className="mr-2 h-4 w-4" />
                            预警记录
                          </DropdownMenuItem>
                          {isCreator && (
                            <DropdownMenuItem
                              onClick={() => handleDeleteTopic(topic)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除Topic
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 权限管理对话框 */}
      <TopicPermissionsDialog
        topic={permissionsTopic}
        groupId={groupId}
        open={!!permissionsTopic}
        onOpenChange={(open) => !open && setPermissionsTopic(null)}
        isCreator={isCreator}
      />

      {/* 历史记录对话框 */}
      <TopicHistoryDialog
        topic={historyTopic}
        open={!!historyTopic}
        onOpenChange={(open) => !open && setHistoryTopic(null)}
      />

      {/* 预警规则管理对话框 */}
      <AlertRulesDialog
        topic={alertRulesTopic}
        groupId={groupId}
        open={!!alertRulesTopic}
        onOpenChange={(open) => !open && setAlertRulesTopic(null)}
        isCreator={isCreator}
      />

      {/* 预警记录查看对话框 */}
      <AlertRecordsDialog
        topic={alertRecordsTopic}
        groupId={groupId}
        open={!!alertRecordsTopic}
        onOpenChange={(open) => !open && setAlertRecordsTopic(null)}
      />

      {/* 删除确认对话框 */}
      <AlertDialog
        open={!!deletingTopic}
        onOpenChange={() => setDeletingTopic(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除Topic</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除Topic "{deletingTopic?.topic_name}" 吗？
              此操作不可撤销，将会删除所有相关的权限设置。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTopic}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteTopicMutation.isPending}
            >
              {deleteTopicMutation.isPending ? '删除中...' : '确认删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}