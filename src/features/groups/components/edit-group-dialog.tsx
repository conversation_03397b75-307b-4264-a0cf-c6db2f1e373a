import { useEffect } from 'react'
import { useMutation, useQueryClient } from '@/hooks/use-api'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { Group, UpdateGroupRequest } from '@/types/groups'

const editGroupSchema = z.object({
  name: z.string().min(1, '分组名称不能为空').max(50, '分组名称不能超过50个字符'),
  description: z.string().min(1, '分组描述不能为空').max(200, '分组描述不能超过200个字符'),
  max_members: z.number().min(1, '最大成员数不能少于1').max(1000, '最大成员数不能超过1000'),
  status: z.enum(['active', 'inactive', 'archived']),
})

interface EditGroupDialogProps {
  group: Group | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function EditGroupDialog({ group, open, onOpenChange }: EditGroupDialogProps) {
  const queryClient = useQueryClient()

  const form = useForm<UpdateGroupRequest>({
    resolver: zodResolver(editGroupSchema),
    defaultValues: {
      name: group?.name || '',
      description: group?.description || '',
      max_members: group?.max_members || 100,
      status: group?.status || 'active',
    },
  })

  // 当group变化时更新表单默认值
  useEffect(() => {
    if (group) {
      form.reset({
        name: group.name,
        description: group.description,
        max_members: group.max_members,
        status: group.status,
      })
    }
  }, [group, form])

  // 更新分组
  const updateGroupMutation = useMutation({
    mutationFn: (data: UpdateGroupRequest) => {
      if (!group) throw new Error('分组信息不存在')
      return GroupsAPI.updateGroup(group.group_id, data)
    },
    onSuccess: () => {
      toast.success('分组信息更新成功')
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.all })
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.detail(group?.group_id || '') })
      onOpenChange(false)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '更新分组信息失败')
    },
  })

  const onSubmit = (data: UpdateGroupRequest) => {
    updateGroupMutation.mutate(data)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset()
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>编辑分组信息</DialogTitle>
          <DialogDescription>
            修改分组的基本信息和设置。
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分组名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入分组名称" {...field} />
                  </FormControl>
                  <FormDescription>
                    分组名称将用于标识和显示分组
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分组描述</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="输入分组描述"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    简要描述分组的用途和目标
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_members"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>最大成员数</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="100"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>
                    分组可容纳的最大成员数量
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分组状态</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择分组状态" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">活跃</SelectItem>
                      <SelectItem value="inactive">非活跃</SelectItem>
                      <SelectItem value="archived">已归档</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    设置分组的当前状态
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={updateGroupMutation.isPending}
              >
                {updateGroupMutation.isPending ? '更新中...' : '保存更改'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
