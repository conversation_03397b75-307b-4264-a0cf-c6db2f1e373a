import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@/hooks/use-api'
import { Check, X, Clock, User, MessageSquare } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { JoinRequest } from '@/types/groups'

interface JoinRequestListProps {
  groupId?: string
  showGroupName?: boolean
}

export function JoinRequestList({ groupId, showGroupName = false }: JoinRequestListProps) {
  const queryClient = useQueryClient()
  const [reviewingRequest, setReviewingRequest] = useState<{
    request: JoinRequest
    action: 'approved' | 'rejected'
  } | null>(null)

  // 获取申请列表
  const { data: requestsData, isLoading } = useQuery({
    queryKey: groupId 
      ? ['groups', 'requests', groupId]
      : ['groups', 'requests', 'all'],
    queryFn: async () => {
      if (groupId) {
        const response = await GroupsAPI.getJoinRequests(groupId, { status: 'pending' })
        return response.data
      } else {
        const response = await GroupsAPI.getAllJoinRequests({ status: 'pending' })
        return response.data
      }
    },
  })

  // 审核申请
  const reviewRequestMutation = useMutation({
    mutationFn: ({ requestId, status }: { requestId: string; status: 'approved' | 'rejected' }) =>
      GroupsAPI.reviewJoinRequest(requestId, { status }),
    onSuccess: (_, { status }) => {
      toast.success(status === 'approved' ? '申请已通过' : '申请已拒绝')
      queryClient.invalidateQueries({ queryKey: ['groups', 'requests'] })
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.all })
      setReviewingRequest(null)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '操作失败')
    },
  })

  const handleReview = (request: JoinRequest, action: 'approved' | 'rejected') => {
    setReviewingRequest({ request, action })
  }

  const confirmReview = () => {
    if (reviewingRequest) {
      reviewRequestMutation.mutate({
        requestId: reviewingRequest.request.id,
        status: reviewingRequest.action,
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待审核'
      case 'approved':
        return '已通过'
      case 'rejected':
        return '已拒绝'
      default:
        return '未知'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const requests = (requestsData as any)?.requests || (requestsData as any)?.items || []

  if (requests.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">暂无待审批的申请</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="space-y-4">
        {requests.map((request: JoinRequest) => (
          <Card key={request.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-base">
                      {request.display_name}
                    </CardTitle>
                    <p className="text-sm text-gray-600">{request.email}</p>
                    {showGroupName && (
                      <p className="text-sm text-gray-500">
                        申请加入：{request.group_name}
                      </p>
                    )}
                  </div>
                </div>
                <Badge className={getStatusColor(request.status)}>
                  {getStatusText(request.status)}
                </Badge>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                {/* 申请理由 */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start gap-2">
                    <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">申请理由</p>
                      <p className="text-sm text-gray-600">{request.message}</p>
                    </div>
                  </div>
                </div>

                {/* 申请时间 */}
                <div className="text-xs text-gray-500">
                  申请时间：{new Date(request.created_at).toLocaleString()}
                </div>

                {/* 操作按钮 */}
                {request.status === 'pending' && (
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleReview(request, 'approved')}
                      disabled={reviewRequestMutation.isPending}
                    >
                      <Check className="w-4 h-4 mr-1" />
                      通过
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleReview(request, 'rejected')}
                      disabled={reviewRequestMutation.isPending}
                    >
                      <X className="w-4 h-4 mr-1" />
                      拒绝
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 确认对话框 */}
      <AlertDialog 
        open={!!reviewingRequest} 
        onOpenChange={() => setReviewingRequest(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              确认{reviewingRequest?.action === 'approved' ? '通过' : '拒绝'}申请
            </AlertDialogTitle>
            <AlertDialogDescription>
              您确定要{reviewingRequest?.action === 'approved' ? '通过' : '拒绝'} 
              {reviewingRequest?.request.display_name} 的加入申请吗？
              {reviewingRequest?.action === 'approved' && '通过后该用户将成为分组成员。'}
              {reviewingRequest?.action === 'rejected' && '拒绝后该用户需要重新申请。'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmReview}
              className={
                reviewingRequest?.action === 'rejected' 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : ''
              }
              disabled={reviewRequestMutation.isPending}
            >
              {reviewRequestMutation.isPending ? '处理中...' : '确认'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
