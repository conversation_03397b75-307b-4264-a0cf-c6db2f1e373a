import { useState } from 'react'
import { useMutation, useQueryClient } from '@/hooks/use-api'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { JoinGroupRequest, Group } from '@/types/groups'

interface JoinGroupDialogProps {
  group?: Group
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function JoinGroupDialog({ group, open, onOpenChange }: JoinGroupDialogProps) {
  const [formData, setFormData] = useState<JoinGroupRequest>({
    group_id: group?.group_id || '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const queryClient = useQueryClient()

  const joinGroupMutation = useMutation({
    mutationFn: (data: JoinGroupRequest) => GroupsAPI.joinGroup(data),
    onSuccess: () => {
      toast.success('加入申请已提交', {
        description: '请等待分组创建者审核您的申请'
      })
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.myGroups() })
      handleClose()
    },
    onError: (error: any) => {
      console.error('Join group error:', error)
      toast.error('申请失败', {
        description: error?.response?.data?.message || '请稍后重试'
      })
    },
    onSettled: () => {
      setIsSubmitting(false)
    }
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.group_id.trim()) {
      toast.error('请输入分组ID')
      return
    }

    if (!formData.message.trim()) {
      toast.error('请输入申请理由')
      return
    }

    setIsSubmitting(true)
    joinGroupMutation.mutate(formData)
  }

  const handleClose = () => {
    setFormData({ group_id: '', message: '' })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>申请加入分组</DialogTitle>
          <DialogDescription>
            {group
              ? `申请加入 "${group.name}" 分组，请说明您的申请理由。`
              : '请输入要加入的分组ID和申请理由，分组创建者将审核您的申请。'
            }
          </DialogDescription>
        </DialogHeader>

        {group && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-sm mb-2">分组信息</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <p><span className="font-medium">名称：</span>{group.name}</p>
              <p><span className="font-medium">描述：</span>{group.description}</p>
              <p><span className="font-medium">成员：</span>{group.member_count}/{group.max_members}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {!group && (
            <div className="space-y-2">
              <Label htmlFor="group_id">分组ID *</Label>
              <Input
                id="group_id"
                placeholder="请输入分组ID"
                value={formData.group_id}
                onChange={(e) => setFormData(prev => ({ ...prev, group_id: e.target.value }))}
                disabled={isSubmitting}
                required
              />
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="message">申请理由 *</Label>
            <Textarea
              id="message"
              placeholder="请说明您加入此分组的理由..."
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              disabled={isSubmitting}
              rows={4}
              required
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? '提交中...' : '提交申请'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
