import { useState } from 'react'
import { useMutation, useQueryClient } from '@/hooks/use-api'
import { Link } from 'react-router-dom'
import { 
  Users, 
  MessageSquare, 
  Settings, 
  MoreVertical, 
  Edit, 
  Trash2, 
  UserPlus,
  ExternalLink 
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import { useAuthStore } from '@/stores/authStore'
import { JoinGroupDialog } from './join-group-dialog'
import { EditGroupDialog } from './edit-group-dialog'
import type { Group } from '@/types/groups'

interface GroupCardProps {
  group: Group
  showJoinButton?: boolean
}

export function GroupCard({ group, showJoinButton = false }: GroupCardProps) {
  const queryClient = useQueryClient()
  const { user } = useAuthStore()
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showJoinDialog, setShowJoinDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)

  // 判断是否为创建者
  const isCreator = user ? group.creator_id === user.id : false

  // 删除分组
  const deleteGroupMutation = useMutation({
    mutationFn: (groupId: string) => GroupsAPI.deleteGroup(groupId),
    onSuccess: () => {
      toast.success('分组删除成功')
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.all })
      setShowDeleteDialog(false)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '删除分组失败')
    },
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃'
      case 'inactive':
        return '非活跃'
      case 'archived':
        return '已归档'
      default:
        return '未知'
    }
  }

  const handleDelete = () => {
    deleteGroupMutation.mutate(group.group_id)
  }

  return (
    <>
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold truncate">
                {group.name}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {group.description}
              </p>
            </div>
            <div className="flex items-center gap-2 ml-2">
              <Badge className={getStatusColor(group.status)}>
                {getStatusText(group.status)}
              </Badge>
              {isCreator && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link to={`/mqtt/groups/${group.group_id}`}>
                        <Settings className="mr-2 h-4 w-4" />
                        管理分组
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
                      <Edit className="mr-2 h-4 w-4" />
                      编辑信息
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => setShowDeleteDialog(true)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除分组
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* 统计信息 */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Users className="w-4 h-4" />
              <span>{group.member_count}/{group.max_members} 成员</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <MessageSquare className="w-4 h-4" />
              <span>{group.topic_count} Topics</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            {isCreator && (
              <Button asChild className="flex-1">
                <Link to={`/mqtt/groups/${group.group_id}`}>
                  <Settings className="w-4 h-4 mr-2" />
                  管理
                </Link>
              </Button>
            )}
            
            {showJoinButton && (
              <Button 
                variant="outline" 
                className="flex-1"
                onClick={() => setShowJoinDialog(true)}
              >
                <UserPlus className="w-4 h-4 mr-2" />
                申请加入
              </Button>
            )}

            <Button variant="outline" size="sm" asChild>
              <Link to={`/mqtt/groups/${group.group_id}`}>
                <ExternalLink className="w-4 h-4" />
              </Link>
            </Button>
          </div>

          {/* 创建时间 */}
          <div className="mt-3 pt-3 border-t text-xs text-gray-500">
            创建于 {new Date(group.created_at).toLocaleDateString()}
          </div>
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除分组</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除分组 "{group.name}" 吗？此操作不可撤销，将会删除分组内的所有Topic和权限设置。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteGroupMutation.isPending}
            >
              {deleteGroupMutation.isPending ? '删除中...' : '确认删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 编辑分组对话框 */}
      <EditGroupDialog
        group={group}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
      />

      {/* 申请加入对话框 */}
      <JoinGroupDialog
        group={group}
        open={showJoinDialog}
        onOpenChange={setShowJoinDialog}
      />
    </>
  )
}
