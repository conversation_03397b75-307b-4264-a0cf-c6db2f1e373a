import { useQuery, useMutation, useQueryClient } from '@/hooks/use-api'
import { useState, useEffect } from 'react'
import { Shield, User, X } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { GroupTopic, GroupMember, TopicPermission } from '@/types/groups'

interface TopicPermissionsDialogProps {
  topic: GroupTopic | null
  groupId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  isCreator?: boolean
}

export function TopicPermissionsDialog({
  topic,
  groupId,
  open,
  onOpenChange,
  isCreator = false
}: TopicPermissionsDialogProps) {
  const queryClient = useQueryClient()

  // 本地状态用于乐观更新
  const [localPermissions, setLocalPermissions] = useState<TopicPermission[]>([])
  const [isUpdating, setIsUpdating] = useState<Record<string, boolean>>({})

  // 获取分组成员
  const { data: membersResponse, isLoading: membersLoading } = useQuery({
    queryKey: groupsQueryKeys.members(groupId),
    queryFn: async () => {
      const response = await GroupsAPI.getMembers(groupId)
      return response.data
    },
    enabled: open && !!topic,
  })

  // 获取Topic权限
  const { data: permissionsResponse, isLoading: permissionsLoading } = useQuery({
    queryKey: groupsQueryKeys.permissions(groupId, topic?.full_name || ''),
    queryFn: async () => {
      if (!topic) return null
      const response = await GroupsAPI.getPermissions(groupId, topic.full_name)
      return response.data
    },
    enabled: open && !!topic,
  })

  // 设置权限
  const setPermissionMutation = useMutation({
    mutationFn: ({ userId, canPub, canSub }: { userId: string; canPub: boolean; canSub: boolean }) => {
      if (!topic) throw new Error('Topic信息不存在')
      return GroupsAPI.setPermission(groupId, {
        user_id: userId,
        full_topic: topic.full_name,
        can_pub: canPub,
        can_sub: canSub,
      })
    },
    onSuccess: (_, variables) => {
      toast.success('权限设置成功')
      // 清除更新状态
      setIsUpdating(prev => ({ ...prev, [variables.userId]: false }))
      // 立即刷新权限数据
      if (topic) {
        queryClient.invalidateQueries({
          queryKey: groupsQueryKeys.permissions(groupId, topic.full_name)
        })
        // 同时刷新权限矩阵数据（如果存在）
        queryClient.invalidateQueries({
          queryKey: ['all-permissions', groupId]
        })
      }
    },
    onError: (error: any, variables) => {
      // 清除更新状态并回滚本地状态
      setIsUpdating(prev => ({ ...prev, [variables.userId]: false }))
      // 重新同步服务端数据
      setLocalPermissions(permissions)
      toast.error(error.response?.data?.message || '权限设置失败')
    },
  })

  // 移除权限
  const removePermissionMutation = useMutation({
    mutationFn: (userId: string) => {
      if (!topic) throw new Error('Topic信息不存在')
      return GroupsAPI.removePermission(groupId, userId, topic.full_name)
    },
    onSuccess: (_, userId) => {
      toast.success('权限已移除')
      // 清除更新状态
      setIsUpdating(prev => ({ ...prev, [userId]: false }))
      // 立即刷新权限数据
      if (topic) {
        queryClient.invalidateQueries({
          queryKey: groupsQueryKeys.permissions(groupId, topic.full_name)
        })
        // 同时刷新权限矩阵数据（如果存在）
        queryClient.invalidateQueries({
          queryKey: ['all-permissions', groupId]
        })
      }
    },
    onError: (error: any, userId) => {
      // 清除更新状态并回滚本地状态
      setIsUpdating(prev => ({ ...prev, [userId]: false }))
      // 重新同步服务端数据
      setLocalPermissions(permissions)
      toast.error(error.response?.data?.message || '移除权限失败')
    },
  })

  const members = membersResponse?.members || []
  const permissions = permissionsResponse?.permissions || []

  // 同步服务端数据到本地状态
  useEffect(() => {
    if (permissions.length > 0) {
      setLocalPermissions(permissions)
    }
  }, [permissions])

  // 获取用户的权限信息（优先使用本地状态）
  const getUserPermission = (userId: string) => {
    return localPermissions.find((p: TopicPermission) => p.user_id === userId) ||
           permissions.find((p: TopicPermission) => p.user_id === userId)
  }

  // 处理权限变更
  const handlePermissionChange = (userId: string, type: 'pub' | 'sub', value: boolean) => {
    const currentPermission = getUserPermission(userId)
    const canPub = type === 'pub' ? value : (currentPermission?.can_pub || false)
    const canSub = type === 'sub' ? value : (currentPermission?.can_sub || false)

    // 乐观更新本地状态
    setIsUpdating(prev => ({ ...prev, [userId]: true }))
    setLocalPermissions(prev => {
      const updated = [...prev]
      const existingIndex = updated.findIndex(p => p.user_id === userId)

      if (existingIndex >= 0) {
        // 更新现有权限
        updated[existingIndex] = {
          ...updated[existingIndex],
          can_pub: canPub,
          can_sub: canSub,
        }
      } else {
        // 添加新权限
        const member = members.find((m: GroupMember) => m.user_id === userId)
        if (member && topic) {
          updated.push({
            id: `temp-${userId}`,
            user_id: userId,
            group_id: groupId,
            full_topic: topic.full_name,
            can_pub: canPub,
            can_sub: canSub,
            updated_at: new Date().toISOString(),
            email: member.email,
            display_name: member.display_name,
          })
        }
      }

      return updated
    })

    setPermissionMutation.mutate({ userId, canPub, canSub })
  }

  // 移除用户的所有权限
  const handleRemovePermission = (userId: string) => {
    // 乐观更新本地状态
    setIsUpdating(prev => ({ ...prev, [userId]: true }))
    setLocalPermissions(prev => prev.filter(p => p.user_id !== userId))

    removePermissionMutation.mutate(userId)
  }

  if (!topic) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Topic权限管理
          </DialogTitle>
          <DialogDescription>
            为 "{topic.topic_name}" 设置成员的发布和订阅权限
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Topic信息 */}
          <div className="p-3 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Topic信息</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <p><span className="font-medium">名称：</span>{topic.topic_name}</p>
              <p><span className="font-medium">完整路径：</span>{topic.full_name}</p>
            </div>
          </div>

          {/* 权限列表 */}
          <div>
            <h4 className="font-medium text-sm mb-3">成员权限</h4>
            
            {membersLoading || permissionsLoading ? (
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center gap-4 p-3 border rounded-lg">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1 space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {members.map((member: GroupMember) => {
                  const permission = getUserPermission(member.user_id)
                  
                  return (
                    <div key={member.id} className="flex items-center gap-4 p-3 border rounded-lg">
                      {/* 用户信息 */}
                      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium truncate">
                            {member.display_name}
                          </p>
                          {member.role === 'creator' && (
                            <Badge variant="outline" className="text-xs">创建者</Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 truncate">{member.email}</p>
                      </div>

                      {/* 权限开关 */}
                      {isCreator && (
                        <div className="flex items-center gap-4">
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`pub-${member.user_id}`}
                              checked={permission?.can_pub || false}
                              onCheckedChange={(checked) => 
                                handlePermissionChange(member.user_id, 'pub', checked)
                              }
                              disabled={setPermissionMutation.isPending || isUpdating[member.user_id]}
                            />
                            <Label htmlFor={`pub-${member.user_id}`} className="text-xs">
                              发布
                            </Label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`sub-${member.user_id}`}
                              checked={permission?.can_sub || false}
                              onCheckedChange={(checked) => 
                                handlePermissionChange(member.user_id, 'sub', checked)
                              }
                              disabled={setPermissionMutation.isPending || isUpdating[member.user_id]}
                            />
                            <Label htmlFor={`sub-${member.user_id}`} className="text-xs">
                              订阅
                            </Label>
                          </div>

                          {permission && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRemovePermission(member.user_id)}
                              disabled={removePermissionMutation.isPending || isUpdating[member.user_id]}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      )}

                      {/* 只读权限显示 */}
                      {!isCreator && (
                        <div className="flex items-center gap-2">
                          <Badge variant={permission?.can_pub ? 'default' : 'secondary'}>
                            发布: {permission?.can_pub ? '允许' : '禁止'}
                          </Badge>
                          <Badge variant={permission?.can_sub ? 'default' : 'secondary'}>
                            订阅: {permission?.can_sub ? '允许' : '禁止'}
                          </Badge>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
