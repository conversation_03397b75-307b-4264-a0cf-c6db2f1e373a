import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@/hooks/use-api'
import { Bell, Check, X, Clock, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { JoinRequest } from '@/types/groups'

export function RequestsNotification() {
  const [showDialog, setShowDialog] = useState(false)
  const queryClient = useQueryClient()

  // 获取所有待处理的申请
  const { data: requestsData, isLoading } = useQuery({
    queryKey: ['groups', 'all-requests', 'pending'],
    queryFn: async () => {
      try {
        const response = await GroupsAPI.getAllJoinRequests({ status: 'pending' })
        return response.data || { requests: [], total: 0 }
      } catch (error) {
        console.error('Error fetching requests:', error)
        return { requests: [], total: 0 }
      }
    },
    // refetchInterval: 30000, // 每30秒刷新一次 - 不支持自动刷新
  })

  // 审核申请
  const reviewRequestMutation = useMutation({
    mutationFn: ({ requestId, status }: { requestId: string; status: 'approved' | 'rejected' }) =>
      GroupsAPI.reviewJoinRequest(requestId, { status }),
    onSuccess: (response) => {
      if (response.code === 200) {
        toast.success('申请处理成功')
        queryClient.invalidateQueries({ queryKey: ['groups', 'all-requests'] })
        queryClient.invalidateQueries({ queryKey: groupsQueryKeys.myGroups() })
      } else {
        toast.error(response.message || '处理申请失败')
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '处理申请失败')
    },
  })

  const handleReviewRequest = (requestId: string, status: 'approved' | 'rejected') => {
    reviewRequestMutation.mutate({ requestId, status })
  }

  const pendingCount = requestsData?.total || 0

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Bell className="w-4 h-4" />
          {pendingCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {pendingCount > 99 ? '99+' : pendingCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            加入申请通知
          </DialogTitle>
          <DialogDescription>
            处理用户的分组加入申请
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-48" />
                        <Skeleton className="h-3 w-64" />
                        <Skeleton className="h-3 w-40" />
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Skeleton className="h-8 w-16" />
                        <Skeleton className="h-8 w-16" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : requestsData?.requests && requestsData.requests.length > 0 ? (
            <div className="space-y-3">
              {requestsData.requests.map((request: JoinRequest & { group_name?: string }) => (
                <Card key={request.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="font-medium text-sm">{request.display_name}</div>
                          <Badge variant="outline" className="text-xs">
                            {request.group_name || request.group_id}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mb-2">{request.email}</div>
                        <div className="text-sm mb-2">
                          <span className="font-medium">申请理由：</span>
                          <span className="text-muted-foreground">{request.message}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          申请时间：{new Date(request.created_at).toLocaleString()}
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() => handleReviewRequest(request.id, 'approved')}
                          disabled={reviewRequestMutation.isPending}
                        >
                          <Check className="w-3 h-3 mr-1" />
                          通过
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() => handleReviewRequest(request.id, 'rejected')}
                          disabled={reviewRequestMutation.isPending}
                        >
                          <X className="w-3 h-3 mr-1" />
                          拒绝
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">暂无申请</h3>
              <p className="text-muted-foreground">
                目前没有待处理的加入申请
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
