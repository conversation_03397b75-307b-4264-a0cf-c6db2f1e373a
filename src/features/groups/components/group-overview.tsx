import { useQuery } from '@/hooks/use-api'
import { Users, MessageSquare, Calendar, Settings } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { GroupDetail } from '@/types/groups'

interface GroupOverviewProps {
  group: GroupDetail
}

export function GroupOverview({ group }: GroupOverviewProps) {
  // 获取分组成员数据
  const { data: membersData, isLoading: membersLoading } = useQuery({
    queryKey: groupsQueryKeys.members(group.group_id),
    queryFn: async () => {
      const response = await GroupsAPI.getMembers(group.group_id)
      return response.data
    },
  })

  // 获取分组Topic数据
  const { data: topicsResponse, isLoading: topicsLoading } = useQuery({
    queryKey: groupsQueryKeys.topics(group.group_id),
    queryFn: async () => {
      const response = await GroupsAPI.getTopics(group.group_id)
      return response.data
    },
  })

  const topics = topicsResponse?.topics || []



  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃'
      case 'inactive':
        return '非活跃'
      case 'archived':
        return '已归档'
      default:
        return '未知'
    }
  }

  const stats = [
    {
      title: '成员数量',
      value: `${group.member_count}/${group.max_members}`,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Topic数量',
      value: group.topic_count,
      icon: MessageSquare,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '创建时间',
      value: new Date(group.created_at).toLocaleDateString(),
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: '分组状态',
      value: getStatusText(group.status),
      icon: Settings,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <div className="space-y-4">
      {/* 基本信息 - 紧凑版 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">分组信息</CardTitle>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {stats.map((stat) => {
              const Icon = stat.icon
              return (
                <div key={stat.title} className="flex items-center gap-2 p-2 rounded-lg bg-muted/30">
                  <div className={`p-1.5 rounded ${stat.bgColor}`}>
                    <Icon className={`h-3 w-3 ${stat.color}`} />
                  </div>
                  <div>
                    <div className="text-sm font-medium">{stat.value}</div>
                    <div className="text-xs text-muted-foreground">{stat.title}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 最近成员 - 紧凑版 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">最近成员</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {membersLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-6 w-6 rounded-full" />
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-2 w-24" />
                  </div>
                </div>
              ))}
            </div>
          ) : membersData && (membersData as any).members && (membersData as any).members.length > 0 ? (
            <div className="space-y-2">
              {(membersData as any).members.slice(0, 4).map((member: any) => (
                <div key={member.id} className="flex items-center gap-2 p-2 rounded bg-muted/20">
                  <div className="h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">
                      {member.display_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium truncate">{member.display_name}</p>
                    <p className="text-xs text-muted-foreground truncate">{member.email}</p>
                  </div>
                  {member.role === 'creator' && (
                    <Badge variant="outline" className="text-xs px-1 py-0">创建者</Badge>
                  )}
                </div>
              ))}
              {(membersData as any).members.length > 4 && (
                <p className="text-xs text-muted-foreground text-center pt-1">
                  还有 {(membersData as any).members.length - 4} 个成员...
                </p>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-3 text-sm">暂无成员</p>
          )}
        </CardContent>
      </Card>

      {/* 最近Topic - 紧凑版 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">最近Topic</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {topicsLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-6 w-6 rounded" />
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-2 w-20" />
                  </div>
                </div>
              ))}
            </div>
          ) : topics && topics.length > 0 ? (
            <div className="space-y-2">
              {topics.slice(0, 4).map((topic) => (
                <div key={topic.id} className="flex items-center gap-2 p-2 rounded bg-muted/20">
                  <div className="h-6 w-6 bg-green-100 rounded flex items-center justify-center">
                    <MessageSquare className="h-3 w-3 text-green-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium truncate">{topic.topic_name}</p>
                    <p className="text-xs text-muted-foreground truncate font-mono">{topic.full_name}</p>
                  </div>
                </div>
              ))}
              {topics.length > 4 && (
                <p className="text-xs text-muted-foreground text-center pt-1">
                  还有 {topics.length - 4} 个Topic...
                </p>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-3 text-sm">暂无Topic</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}