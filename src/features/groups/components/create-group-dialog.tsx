
import { useMutation, useQueryClient } from '@/hooks/use-api'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { CreateGroupFormData } from '@/types/groups'

const createGroupSchema = z.object({
  name: z.string().min(1, '分组名称不能为空').max(50, '分组名称不能超过50个字符'),
  description: z.string().min(1, '分组描述不能为空').max(200, '分组描述不能超过200个字符'),
  max_members: z.number().min(1, '最大成员数不能少于1').max(1000, '最大成员数不能超过1000'),
})

interface CreateGroupDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateGroupDialog({ open, onOpenChange }: CreateGroupDialogProps) {
  const queryClient = useQueryClient()

  const form = useForm<CreateGroupFormData>({
    resolver: zodResolver(createGroupSchema),
    defaultValues: {
      name: '',
      description: '',
      max_members: 100,
    },
  })

  // 创建分组
  const createGroupMutation = useMutation({
    mutationFn: (data: CreateGroupFormData) => GroupsAPI.createGroup(data),
    onSuccess: () => {
      toast.success('分组创建成功')
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.all })
      form.reset()
      onOpenChange(false)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '创建分组失败')
    },
  })

  const onSubmit = (data: CreateGroupFormData) => {
    createGroupMutation.mutate(data)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset()
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>创建新分组</DialogTitle>
          <DialogDescription>
            创建一个新的MQTT分组来管理Topic和成员权限。
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分组名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入分组名称" {...field} />
                  </FormControl>
                  <FormDescription>
                    分组名称将用于标识和显示分组
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分组描述</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="输入分组描述"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    简要描述分组的用途和目标
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_members"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>最大成员数</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="100"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>
                    分组可容纳的最大成员数量
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={createGroupMutation.isPending}
              >
                {createGroupMutation.isPending ? '创建中...' : '创建分组'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
