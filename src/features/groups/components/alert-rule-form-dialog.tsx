import { useEffect } from 'react'
import { useMutation } from '@/hooks/use-api'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Plus, Trash2 } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { toast } from 'sonner'
import { GroupsAPI } from '@/api/groups'
import type { GroupTopic, AlertRule, CreateAlertRuleRequest, UpdateAlertRuleRequest } from '@/types/groups'

const alertConditionSchema = z.object({
  field: z.string().min(1, '字段名不能为空'),
  operator: z.string().min(1, '操作符不能为空'),
  value: z.string().min(1, '值不能为空'),
  data_type: z.enum(['number', 'string', 'bool']),
})

const alertRuleSchema = z.object({
  rule_name: z.string().min(1, '规则名称不能为空').max(100, '规则名称不能超过100个字符'),
  rule_type: z.string().min(1, '规则类型不能为空'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  conditions: z.array(alertConditionSchema).min(1, '至少需要一个条件'),
  level: z.number().min(1).max(5),
  notification: z.object({
    enabled: z.boolean(),
    channels: z.array(z.string()),
    recipients: z.array(z.string()),
  }),
  enabled: z.boolean(),
})

type AlertRuleFormData = z.infer<typeof alertRuleSchema>

interface AlertRuleFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  topic: GroupTopic | null
  groupId: string
  rule?: AlertRule | null
  onSuccess: () => void
}

export function AlertRuleFormDialog({
  open,
  onOpenChange,
  topic,
  groupId,
  rule,
  onSuccess,
}: AlertRuleFormDialogProps) {
  const isEditing = !!rule

  const form = useForm<AlertRuleFormData>({
    resolver: zodResolver(alertRuleSchema),
    defaultValues: {
      rule_name: '',
      rule_type: 'threshold',
      description: '',
      conditions: [
        {
          field: '',
          operator: '>',
          value: '',
          data_type: 'number',
        },
      ],
      level: 3,
      notification: {
        enabled: true,
        channels: ['email'],
        recipients: [],
      },
      enabled: true,
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'conditions',
  })

  // 创建预警规则
  const createRuleMutation = useMutation({
    mutationFn: (data: CreateAlertRuleRequest) => GroupsAPI.createAlertRule(data),
    onSuccess: () => {
      toast.success('预警规则创建成功')
      onSuccess()
      form.reset()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '创建预警规则失败')
    },
  })

  // 更新预警规则
  const updateRuleMutation = useMutation({
    mutationFn: ({ ruleId, data }: { ruleId: string; data: UpdateAlertRuleRequest }) =>
      GroupsAPI.updateAlertRule(ruleId, data),
    onSuccess: () => {
      toast.success('预警规则更新成功')
      onSuccess()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '更新预警规则失败')
    },
  })

  // 当编辑规则时，填充表单数据
  useEffect(() => {
    if (rule && open) {
      form.reset({
        rule_name: rule.rule_name,
        rule_type: rule.rule_type,
        description: rule.description,
        conditions: rule.conditions,
        level: rule.level,
        notification: rule.notification,
        enabled: rule.enabled,
      })
    } else if (!rule && open) {
      form.reset({
        rule_name: '',
        rule_type: 'threshold',
        description: '',
        conditions: [
          {
            field: '',
            operator: '>',
            value: '',
            data_type: 'number',
          },
        ],
        level: 3,
        notification: {
          enabled: true,
          channels: ['email'],
          recipients: [],
        },
        enabled: true,
      })
    }
  }, [rule, open, form])

  const onSubmit = (data: AlertRuleFormData) => {
    if (!topic) return

    if (isEditing && rule) {
      updateRuleMutation.mutate({
        ruleId: rule.id,
        data: {
          rule_name: data.rule_name,
          rule_type: data.rule_type,
          description: data.description,
          conditions: data.conditions,
          level: data.level,
          notification: data.notification,
          enabled: data.enabled,
        },
      })
    } else {
      createRuleMutation.mutate({
        group_id: groupId,
        topic: topic.full_name,
        rule_name: data.rule_name,
        rule_type: data.rule_type,
        description: data.description || '',
        conditions: data.conditions,
        level: data.level,
        notification: data.notification,
        enabled: data.enabled,
      })
    }
  }

  const addCondition = () => {
    append({
      field: '',
      operator: '>',
      value: '',
      data_type: 'number',
    })
  }

  const removeCondition = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const getOperatorOptions = (dataType: string) => {
    switch (dataType) {
      case 'number':
        return [
          { value: '>', label: '大于 ( > )' },
          { value: '<', label: '小于 (<)' },
          { value: '>=', label: '大于等于 (>=)' },
          { value: '<=', label: '小于等于 (<=)' },
          { value: '==', label: '等于 (==)' },
          { value: '!=', label: '不等于 (!=)' },
        ]
      case 'string':
        return [
          { value: 'equals', label: '等于' },
          { value: 'contains', label: '包含' },
          { value: 'startsWith', label: '开始于' },
          { value: 'endsWith', label: '结束于' },
        ]
      case 'bool':
        return [
          { value: '==', label: '等于 (==)' },
          { value: '!=', label: '不等于 (!=)' },
        ]
      default:
        return []
    }
  }

  if (!topic) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? '编辑预警规则' : '创建预警规则'}
          </DialogTitle>
          <DialogDescription>
            为Topic "{topic.topic_name}" {isEditing ? '编辑' : '创建'}预警规则
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Topic信息 */}
            <div className="p-4 bg-muted/50 rounded-lg border">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-2 w-2 bg-primary rounded-full"></div>
                <span className="text-sm font-medium">目标Topic</span>
              </div>
              <p className="text-sm text-muted-foreground font-mono">{topic?.full_name}</p>
            </div>

            {/* 基本信息 */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="rule_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>规则名称 *</FormLabel>
                      <FormControl>
                        <Input placeholder="温度过高预警" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rule_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>规则类型 *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择规则类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="threshold">阈值预警</SelectItem>
                          <SelectItem value="rate">变化率预警</SelectItem>
                          <SelectItem value="missing">缺失数据预警</SelectItem>
                          <SelectItem value="anomaly">异常值预警</SelectItem>
                          <SelectItem value="composite">复合条件预警</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="当温度超过35度时触发预警"
                        rows={2}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>预警级别 *</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        value={field.value.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择级别" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">1 - 信息</SelectItem>
                          <SelectItem value="2">2 - 警告</SelectItem>
                          <SelectItem value="3">3 - 错误</SelectItem>
                          <SelectItem value="4">4 - 严重</SelectItem>
                          <SelectItem value="5">5 - 灾难</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel className="text-sm font-medium">启用规则</FormLabel>
                      <FormDescription className="text-xs">
                        是否立即启用此预警规则
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* 预警条件 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">预警条件</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addCondition}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  添加条件
                </Button>
              </div>

              <div className="space-y-3">
                {fields.map((field, index) => (
                  <div key={field.id} className="p-3 border rounded-lg bg-muted/30">
                    <div className="grid grid-cols-5 gap-3 items-end">
                      <FormField
                        control={form.control}
                        name={`conditions.${index}.field`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-xs">字段名</FormLabel>
                            <FormControl>
                              <Input placeholder="temperature" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`conditions.${index}.data_type`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-xs">类型</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="number">数值</SelectItem>
                                <SelectItem value="string">字符串</SelectItem>
                                <SelectItem value="bool">布尔值</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`conditions.${index}.operator`}
                        render={({ field: operatorField }) => {
                          const dataType = form.watch(`conditions.${index}.data_type`)
                          const options = getOperatorOptions(dataType)

                          return (
                            <FormItem>
                              <FormLabel className="text-xs">操作符</FormLabel>
                              <Select onValueChange={operatorField.onChange} value={operatorField.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {options.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />

                      <FormField
                        control={form.control}
                        name={`conditions.${index}.value`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-xs">值</FormLabel>
                            <FormControl>
                              <Input placeholder="35" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(index)}
                        disabled={fields.length === 1}
                        className="h-9"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 通知配置 */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium">通知配置</h3>

              <FormField
                control={form.control}
                name="notification.enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel className="text-sm font-medium">启用通知</FormLabel>
                      <FormDescription className="text-xs">
                        是否在触发预警时发送通知
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {form.watch('notification.enabled') && (
                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="notification.channels"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">通知渠道</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => field.onChange([value])}
                            value={field.value[0] || ''}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="选择渠道" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="email">邮件</SelectItem>
                              <SelectItem value="sms">短信</SelectItem>
                              <SelectItem value="wechat">微信</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notification.recipients"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">接收人</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="<EMAIL>"
                            rows={1}
                            value={field.value.join('\n')}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value
                                  .split('\n')
                                  .map(line => line.trim())
                                  .filter(line => line.length > 0)
                              )
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={createRuleMutation.isPending || updateRuleMutation.isPending}
              >
                {createRuleMutation.isPending || updateRuleMutation.isPending
                  ? (isEditing ? '更新中...' : '创建中...')
                  : (isEditing ? '更新规则' : '创建规则')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
