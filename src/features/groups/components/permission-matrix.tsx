import { useState, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@/hooks/use-api'
import { toast } from 'sonner'
import { Shield, Users, MessageSquare, Check, X, Plus, Settings } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import type { TopicPermission, GroupMember, GroupTopic, SetPermissionRequest } from '@/types/groups'

interface PermissionMatrixProps {
  groupId: string
  isCreator?: boolean
}

interface PermissionData {
  userId: string
  displayName: string
  email: string
  permissions: Record<string, { can_pub: boolean; can_sub: boolean }>
}

export function PermissionMatrix({ groupId, isCreator = false }: PermissionMatrixProps) {
  const queryClient = useQueryClient()
  const [showBatchDialog, setShowBatchDialog] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectedTopic, setSelectedTopic] = useState<string>('')
  const [batchPermissions, setBatchPermissions] = useState({ can_pub: false, can_sub: false })

  // 并行获取数据
  const { data: membersData, isLoading: membersLoading } = useQuery({
    queryKey: groupsQueryKeys.members(groupId),
    queryFn: async () => {
      const response = await GroupsAPI.getMembers(groupId)
      return response.data
    },
  })

  const { data: topicsResponse, isLoading: topicsLoading } = useQuery({
    queryKey: groupsQueryKeys.topics(groupId),
    queryFn: async () => {
      const response = await GroupsAPI.getTopics(groupId)
      return response.data
    },
  })

  const topics = topicsResponse?.topics || []

  // 获取所有Topic的权限数据
  const topicPermissionQueries = useQuery({
    queryKey: ['all-permissions', groupId],
    queryFn: async () => {
      if (!topics || topics.length === 0) return {}
      
      const permissionPromises = topics.map(async (topic: GroupTopic) => {
        try {
          const response = await GroupsAPI.getPermissions(groupId, topic.full_name)
          return {
            topicFullName: topic.full_name,
            permissions: response.data?.items || []
          }
        } catch (error) {
          console.error(`Error fetching permissions for ${topic.full_name}:`, error)
          return {
            topicFullName: topic.full_name,
            permissions: []
          }
        }
      })

      const results = await Promise.all(permissionPromises)
      const permissionMap: Record<string, TopicPermission[]> = {}
      
      results.forEach(result => {
        permissionMap[result.topicFullName] = result.permissions
      })
      
      return permissionMap
    },
    enabled: !!topics && topics.length > 0,
  })

  // 构建权限矩阵数据
  const permissionMatrix = useMemo(() => {
    if (!membersData?.items || !topics || !topicPermissionQueries.data) {
      return []
    }

    return membersData.items.map((member: GroupMember) => {
      const permissions: Record<string, { can_pub: boolean; can_sub: boolean }> = {}
      
      topics.forEach((topic: GroupTopic) => {
        const topicPermissions = topicPermissionQueries.data[topic.full_name] || []
        const userPermission = topicPermissions.find(p => p.user_id === member.user_id)
        
        permissions[topic.full_name] = {
          can_pub: userPermission?.can_pub || false,
          can_sub: userPermission?.can_sub || false,
        }
      })

      return {
        userId: member.user_id,
        displayName: member.display_name,
        email: member.email,
        permissions,
      }
    })
  }, [membersData, topics, topicPermissionQueries.data])

  // 设置权限
  const setPermissionMutation = useMutation({
    mutationFn: (data: SetPermissionRequest) => GroupsAPI.setPermission(groupId, data),
    onSuccess: () => {
      toast.success('权限更新成功')
      queryClient.invalidateQueries({ queryKey: ['all-permissions', groupId] })
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '权限更新失败')
    },
  })

  // 移除权限
  const removePermissionMutation = useMutation({
    mutationFn: ({ userId, fullTopic }: { userId: string; fullTopic: string }) =>
      GroupsAPI.removePermission(groupId, userId, fullTopic),
    onSuccess: () => {
      toast.success('权限移除成功')
      queryClient.invalidateQueries({ queryKey: ['all-permissions', groupId] })
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '权限移除失败')
    },
  })

  const handlePermissionChange = (userId: string, fullTopic: string, type: 'pub' | 'sub', value: boolean) => {
    const currentPermission = permissionMatrix.find(p => p.userId === userId)?.permissions[fullTopic]
    
    if (!currentPermission) return

    const newPermission = {
      user_id: userId,
      full_topic: fullTopic,
      can_pub: type === 'pub' ? value : currentPermission.can_pub,
      can_sub: type === 'sub' ? value : currentPermission.can_sub,
    }

    // 如果两个权限都为false，则移除权限
    if (!newPermission.can_pub && !newPermission.can_sub) {
      removePermissionMutation.mutate({ userId, fullTopic })
    } else {
      setPermissionMutation.mutate(newPermission)
    }
  }

  const handleBatchPermissionSet = () => {
    if (!selectedTopic || selectedUsers.length === 0) return

    const promises = selectedUsers.map(userId => {
      const data: SetPermissionRequest = {
        user_id: userId,
        full_topic: selectedTopic,
        can_pub: batchPermissions.can_pub,
        can_sub: batchPermissions.can_sub,
      }
      return GroupsAPI.setPermission(groupId, data)
    })

    Promise.all(promises)
      .then(() => {
        toast.success(`已为 ${selectedUsers.length} 个用户设置权限`)
        queryClient.invalidateQueries({ queryKey: ['all-permissions', groupId] })
        setShowBatchDialog(false)
        setSelectedUsers([])
        setSelectedTopic('')
        setBatchPermissions({ can_pub: false, can_sub: false })
      })
      .catch((error) => {
        toast.error('批量设置权限失败')
        console.error(error)
      })
  }

  const isLoading = membersLoading || topicsLoading || topicPermissionQueries.isLoading

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton key={index} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!topics || topics.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            权限矩阵
          </CardTitle>
          <CardDescription>
            暂无Topic，请先创建Topic后再设置权限
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-base">
                <Shield className="w-4 h-4" />
                权限矩阵
              </CardTitle>
              <CardDescription className="text-xs">
                统一管理所有成员的Topic权限，支持批量操作
              </CardDescription>
            </div>
            {isCreator && (
              <Dialog open={showBatchDialog} onOpenChange={setShowBatchDialog}>
                <DialogTrigger asChild>
                  <Button size="sm" className="h-8 text-xs">
                    <Plus className="w-3 h-3 mr-1" />
                    批量设置
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>批量设置权限</DialogTitle>
                    <DialogDescription>
                      为多个用户同时设置相同的Topic权限
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">选择Topic</label>
                      <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择要设置权限的Topic" />
                        </SelectTrigger>
                        <SelectContent>
                          {topics.map((topic: GroupTopic) => (
                            <SelectItem key={topic.id} value={topic.full_name}>
                              {topic.full_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">选择用户</label>
                      <div className="space-y-2 max-h-32 overflow-y-auto border rounded p-2">
                        {membersData?.items?.map((member: GroupMember) => (
                          <div key={member.user_id} className="flex items-center space-x-2">
                            <Checkbox
                              checked={selectedUsers.includes(member.user_id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedUsers([...selectedUsers, member.user_id])
                                } else {
                                  setSelectedUsers(selectedUsers.filter(id => id !== member.user_id))
                                }
                              }}
                            />
                            <span className="text-sm">{member.display_name}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">权限设置</label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={batchPermissions.can_pub}
                            onCheckedChange={(checked) => 
                              setBatchPermissions(prev => ({ ...prev, can_pub: !!checked }))
                            }
                          />
                          <span className="text-sm">发布权限</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={batchPermissions.can_sub}
                            onCheckedChange={(checked) => 
                              setBatchPermissions(prev => ({ ...prev, can_sub: !!checked }))
                            }
                          />
                          <span className="text-sm">订阅权限</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowBatchDialog(false)}>
                      取消
                    </Button>
                    <Button 
                      onClick={handleBatchPermissionSet}
                      disabled={!selectedTopic || selectedUsers.length === 0 || (!batchPermissions.can_pub && !batchPermissions.can_sub)}
                    >
                      设置权限
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-48">成员</TableHead>
                  {topics.map((topic: GroupTopic) => (
                    <TableHead key={topic.id} className="text-center min-w-24">
                      <div className="space-y-1">
                        <div className="font-medium text-xs">{topic.topic_name}</div>
                        <div className="text-xs text-muted-foreground">发布/订阅</div>
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {permissionMatrix.map((member) => (
                  <TableRow key={member.userId}>
                    <TableCell>
                      <div>
                        <div className="font-medium text-sm">{member.displayName}</div>
                        <div className="text-xs text-muted-foreground">{member.email}</div>
                      </div>
                    </TableCell>
                    {topics.map((topic: GroupTopic) => {
                      const permission = member.permissions[topic.full_name]
                      return (
                        <TableCell key={topic.id} className="text-center">
                          <div className="flex flex-col items-center gap-1">
                            <div className="flex items-center gap-1">
                              <Checkbox
                                checked={permission.can_pub}
                                onCheckedChange={(checked) => 
                                  isCreator && handlePermissionChange(member.userId, topic.full_name, 'pub', !!checked)
                                }
                                disabled={!isCreator}
                                className="h-3 w-3"
                              />
                              <span className="text-xs">发布</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Checkbox
                                checked={permission.can_sub}
                                onCheckedChange={(checked) => 
                                  isCreator && handlePermissionChange(member.userId, topic.full_name, 'sub', !!checked)
                                }
                                disabled={!isCreator}
                                className="h-3 w-3"
                              />
                              <span className="text-xs">订阅</span>
                            </div>
                          </div>
                        </TableCell>
                      )
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 权限说明 */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-1 text-xs text-muted-foreground">
            <p>• 权限矩阵显示所有成员对各个Topic的权限状态</p>
            <p>• 勾选复选框可直接修改权限，变更会立即生效</p>
            <p>• 使用批量设置功能可为多个用户同时分配相同权限</p>
            <p>• 分组创建者默认拥有所有Topic的完整权限</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
