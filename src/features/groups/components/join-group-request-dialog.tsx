import { useState } from 'react'
import { useMutation, useQueryClient } from '@/hooks/use-api'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { UserPlus } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'

const joinGroupSchema = z.object({
  group_id: z.string().min(1, '分组ID不能为空'),
  message: z.string().min(1, '申请理由不能为空').max(500, '申请理由不能超过500个字符'),
})

type JoinGroupFormData = z.infer<typeof joinGroupSchema>

interface JoinGroupRequestDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function JoinGroupRequestDialog({ open, onOpenChange }: JoinGroupRequestDialogProps) {
  const queryClient = useQueryClient()

  const form = useForm<JoinGroupFormData>({
    resolver: zodResolver(joinGroupSchema),
    defaultValues: {
      group_id: '',
      message: '',
    },
  })

  // 提交加入申请
  const joinGroupMutation = useMutation({
    mutationFn: (data: JoinGroupFormData) => GroupsAPI.joinGroup(data),
    onSuccess: () => {
      toast.success('加入申请已提交，请等待分组创建者审核')
      queryClient.invalidateQueries({ queryKey: groupsQueryKeys.all })
      onOpenChange(false)
      form.reset()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '提交申请失败')
    },
  })

  const onSubmit = (data: JoinGroupFormData) => {
    joinGroupMutation.mutate(data)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset()
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            申请加入分组
          </DialogTitle>
          <DialogDescription>
            请输入要加入的分组ID和申请理由，分组创建者将审核您的申请。
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="group_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分组ID</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="请输入分组ID（例如：abc123def456）" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    请向分组创建者获取准确的分组ID
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>申请理由</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="请说明您加入分组的原因和目的..."
                      className="resize-none"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    详细的申请理由有助于提高审核通过率
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={joinGroupMutation.isPending}
              >
                {joinGroupMutation.isPending ? '提交中...' : '提交申请'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
