import { useState, useEffect } from 'react'
import { useQuery, useMutation } from '@/hooks/use-api'
import {
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  TestTube,
  Power,
  PowerOff,
  MoreVertical,
  Search,
  X,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { DataTablePagination } from '@/components/ui/data-table-pagination'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { GroupsAPI, groupsQueryKeys } from '@/api/groups'
import { AlertRuleFormDialog } from './alert-rule-form-dialog'
import type { GroupTopic, AlertRule, GetAlertRulesParams } from '@/types/groups'

// 实际API响应的数据结构
interface AlertRulesApiResponse {
  rules: AlertRule[]
  total: number
  page: number
  size: number
}

interface AlertRulesDialogProps {
  topic: GroupTopic | null
  groupId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  isCreator?: boolean
}

export function AlertRulesDialog({ 
  topic, 
  groupId, 
  open, 
  onOpenChange, 
  isCreator = false 
}: AlertRulesDialogProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null)
  const [deletingRule, setDeletingRule] = useState<AlertRule | null>(null)

  // 构建查询参数
  const queryParams: GetAlertRulesParams = {
    page: currentPage,
    page_size: pageSize,
    group_id: groupId,
    topic: topic?.full_name,
    ...(searchTerm && { search: searchTerm })
  }

  // 获取预警规则列表
  const { data: rulesResponse, isLoading, refetch } = useQuery({
    queryKey: groupsQueryKeys.alertRules(queryParams),
    queryFn: async () => {
      if (!topic?.full_name) return null
      const response = await GroupsAPI.getAlertRules(queryParams)
      // 将API响应的 rules 字段映射为 items 字段以符合 PaginatedResponse 接口
      const data = response.data as unknown as AlertRulesApiResponse
      return {
        items: data.rules || [],
        total: data.total || 0,
        page: data.page || 1,
        size: data.size || pageSize
      }
    },
    enabled: open && !!topic?.full_name,
  })

  const rules = rulesResponse?.items || []
  const total = rulesResponse?.total || 0
  const totalPages = Math.ceil(total / pageSize)

  // 删除预警规则
  const deleteRuleMutation = useMutation({
    mutationFn: (ruleId: string) => GroupsAPI.deleteAlertRule(ruleId),
    onSuccess: () => {
      toast.success('预警规则删除成功')
      refetch()
      setDeletingRule(null)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '删除预警规则失败')
    },
  })

  // 切换规则启用状态
  const toggleRuleMutation = useMutation({
    mutationFn: ({ ruleId, enabled }: { ruleId: string; enabled: boolean }) =>
      GroupsAPI.updateAlertRule(ruleId, { enabled }),
    onSuccess: () => {
      toast.success('预警规则状态更新成功')
      refetch()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '更新预警规则状态失败')
    },
  })

  // 测试预警规则
  const testRuleMutation = useMutation({
    mutationFn: ({ ruleId, testData }: { ruleId: string; testData: any }) =>
      GroupsAPI.testAlertRule(ruleId, testData),
    onSuccess: () => {
      toast.success('预警规则测试成功')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '预警规则测试失败')
    },
  })

  // 重置状态当对话框关闭时
  useEffect(() => {
    if (!open) {
      setCurrentPage(1)
      setSearchTerm('')
      setShowCreateDialog(false)
      setEditingRule(null)
      setDeletingRule(null)
    }
  }, [open])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleDeleteRule = (rule: AlertRule) => {
    setDeletingRule(rule)
  }

  const confirmDeleteRule = () => {
    if (deletingRule) {
      deleteRuleMutation.mutate(deletingRule.id)
    }
  }

  const handleToggleRule = (rule: AlertRule) => {
    toggleRuleMutation.mutate({
      ruleId: rule.id,
      enabled: !rule.enabled
    })
  }

  const handleTestRule = (rule: AlertRule) => {
    // 简单的测试数据，实际应该根据规则条件生成
    const testData = { temperature: 40, humidity: 60 }
    testRuleMutation.mutate({
      ruleId: rule.id,
      testData
    })
  }

  const handleEditRule = (rule: AlertRule) => {
    setEditingRule(rule)
  }

  const handleCreateSuccess = () => {
    setShowCreateDialog(false)
    refetch()
  }

  const handleEditSuccess = () => {
    setEditingRule(null)
    refetch()
  }

  const getLevelBadgeVariant = (level: number) => {
    switch (level) {
      case 1: return 'secondary'
      case 2: return 'default'
      case 3: return 'destructive'
      case 4: return 'destructive'
      case 5: return 'destructive'
      default: return 'outline'
    }
  }

  const getLevelText = (level: number) => {
    switch (level) {
      case 1: return '信息'
      case 2: return '警告'
      case 3: return '错误'
      case 4: return '严重'
      case 5: return '灾难'
      default: return '未知'
    }
  }

  // 过滤规则
  const filteredRules = rules.filter((rule: AlertRule) => {
    if (!searchTerm) return true
    const searchLower = searchTerm.toLowerCase()
    return (
      rule.rule_name.toLowerCase().includes(searchLower) ||
      rule.description.toLowerCase().includes(searchLower)
    )
  })

  if (!topic) return null

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] max-w-[95vw] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              预警规则管理
            </DialogTitle>
            <DialogDescription>
              管理Topic "{topic.topic_name}" 的预警规则
            </DialogDescription>
          </DialogHeader>

          {/* 控制栏 */}
          <div className="flex flex-col gap-4 border-b pb-4">
            <div className="flex items-center justify-between gap-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索规则名称或描述..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => setSearchTerm('')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {isCreator && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建预警规则
                </Button>
              )}
            </div>
          </div>

          {/* 规则列表 */}
          <div className="flex-1 min-h-0">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center gap-4 p-4 border rounded">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 flex-1" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            ) : filteredRules.length === 0 ? (
              <div className="flex items-center justify-center h-32 text-gray-500">
                <div className="text-center">
                  <AlertTriangle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p>{searchTerm ? '没有找到匹配的预警规则' : '暂无预警规则'}</p>
                  {isCreator && !searchTerm && (
                    <Button 
                      className="mt-4" 
                      onClick={() => setShowCreateDialog(true)}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      创建第一个预警规则
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <ScrollArea className="h-full" orientation="horizontal">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>规则名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>条件</TableHead>
                      <TableHead>级别</TableHead>
                      <TableHead>通知</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="w-20">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRules.map((rule: AlertRule) => (
                      <TableRow key={rule.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{rule.rule_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {rule.description}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{rule.rule_type}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {rule.conditions.map((condition, index) => (
                              <div key={index} className="text-xs bg-muted/50 px-2 py-1 rounded">
                                <span className="font-mono">{condition.field}</span>
                                <span className="mx-1">{condition.operator}</span>
                                <span className="font-mono">{condition.value}</span>
                                <span className="ml-1 text-muted-foreground">({condition.data_type})</span>
                              </div>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getLevelBadgeVariant(rule.level)}>
                            {getLevelText(rule.level)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Badge variant={rule.notification.enabled ? 'default' : 'secondary'} className="text-xs">
                              {rule.notification.enabled ? rule.notification.channels.join(', ')  : '已禁用'}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={rule.enabled ? 'default' : 'secondary'}>
                            {rule.enabled ? '启用' : '禁用'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {new Date(rule.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleTestRule(rule)}>
                                <TestTube className="mr-2 h-4 w-4" />
                                测试规则
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleRule(rule)}>
                                {rule.enabled ? (
                                  <>
                                    <PowerOff className="mr-2 h-4 w-4" />
                                    禁用
                                  </>
                                ) : (
                                  <>
                                    <Power className="mr-2 h-4 w-4" />
                                    启用
                                  </>
                                )}
                              </DropdownMenuItem>
                              {isCreator && (
                                <>
                                  <DropdownMenuItem onClick={() => handleEditRule(rule)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    编辑
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleDeleteRule(rule)}
                                    className="text-destructive"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    删除
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            )}
          </div>

          {/* 分页 */}
          {!isLoading && total > 0 && (
            <div className="border-t pt-4">
              <DataTablePagination
                mode="server"
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={total}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                isLoading={isLoading}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 创建预警规则对话框 */}
      <AlertRuleFormDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        topic={topic}
        groupId={groupId}
        onSuccess={handleCreateSuccess}
      />

      {/* 编辑预警规则对话框 */}
      <AlertRuleFormDialog
        open={!!editingRule}
        onOpenChange={(open: boolean) => !open && setEditingRule(null)}
        topic={topic}
        groupId={groupId}
        rule={editingRule}
        onSuccess={handleEditSuccess}
      />

      {/* 删除确认对话框 */}
      <AlertDialog
        open={!!deletingRule}
        onOpenChange={() => setDeletingRule(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除预警规则</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除预警规则 "{deletingRule?.rule_name}" 吗？
              此操作不可撤销，将会删除所有相关的预警记录。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteRule}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteRuleMutation.isPending}
            >
              {deleteRuleMutation.isPending ? '删除中...' : '确认删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
