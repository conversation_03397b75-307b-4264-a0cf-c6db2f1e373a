import { useEffect, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { AuthAPI } from '@/api/auth'
import { useAuthStore } from '@/stores/authStore'

const profileFormSchema = z.object({
  first_name: z.string().min(1, '请输入名字').max(50, '名字不能超过50个字符'),
  last_name: z.string().min(1, '请输入姓氏').max(50, '姓氏不能超过50个字符'),
  display_name: z.string().min(1, '请输入显示名称').max(100, '显示名称不能超过100个字符'),
  company: z.string().max(100, '公司名称不能超过100个字符').optional(),
  department: z.string().max(100, '部门名称不能超过100个字符').optional(),
  avatar: z.string().url('请输入有效的头像URL').optional().or(z.literal('')),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

export default function ProfileForm() {
  const { user, setUser } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      display_name: '',
      company: '',
      department: '',
      avatar: '',
    },
    mode: 'onChange',
  })

  // 加载用户数据
  useEffect(() => {
    if (user?.profile) {
      form.setValue('first_name', user.profile.first_name || '')
      form.setValue('last_name', user.profile.last_name || '')
      form.setValue('display_name', user.profile.display_name || '')
      form.setValue('company', user.profile.company || '')
      form.setValue('department', user.profile.department || '')
      form.setValue('avatar', user.profile.avatar || '')
    }
  }, [user, form])

  // 更新用户资料
  async function onSubmit(data: ProfileFormValues) {
    setIsLoading(true)

    try {
      const response = await AuthAPI.updateProfile({
        profile: {
          first_name: data.first_name,
          last_name: data.last_name,
          display_name: data.display_name,
          company: data.company,
          department: data.department,
          avatar: data.avatar,
        },
      })

      if (response.success && response.data) {
        setUser(response.data)
        toast.success('资料更新成功')
      } else {
        toast.error(response.message || '更新失败')
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      toast.error(error.response?.data?.message || '更新失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='space-y-8'
      >
        <FormField
          control={form.control}
          name='first_name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>名字</FormLabel>
              <FormControl>
                <Input placeholder='请输入您的名字' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='last_name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>姓氏</FormLabel>
              <FormControl>
                <Input placeholder='请输入您的姓氏' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='display_name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>显示名称</FormLabel>
              <FormControl>
                <Input placeholder='请输入显示名称' {...field} />
              </FormControl>
              <FormDescription>
                这是其他人看到的您的名称。
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='company'
          render={({ field }) => (
            <FormItem>
              <FormLabel>公司</FormLabel>
              <FormControl>
                <Input placeholder='请输入公司名称' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='department'
          render={({ field }) => (
            <FormItem>
              <FormLabel>部门</FormLabel>
              <FormControl>
                <Input placeholder='请输入部门名称' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='avatar'
          render={({ field }) => (
            <FormItem>
              <FormLabel>头像URL</FormLabel>
              <FormControl>
                <Input placeholder='请输入头像URL（可选）' {...field} />
              </FormControl>
              <FormDescription>
                输入您的头像图片URL地址。
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type='submit' disabled={isLoading}>
          {isLoading ? '更新中...' : '更新资料'}
        </Button>
      </form>
    </Form>
  )
}
