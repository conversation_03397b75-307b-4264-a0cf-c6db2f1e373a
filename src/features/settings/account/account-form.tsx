import { useEffect, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/custom_components/password-input'
import { Separator } from '@/components/ui/separator'
import { AuthAPI } from '@/api/auth'
import { useAuthStore } from '@/stores/authStore'

// 账户信息（只读显示，不需要表单验证）

// 修改密码表单
const passwordFormSchema = z.object({
  old_password: z.string().min(1, '请输入当前密码'),
  new_password: z.string().min(8, '新密码至少需要8个字符'),
  confirm_password: z.string().min(1, '请确认新密码'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: '两次输入的密码不一致',
  path: ['confirm_password'],
})

type PasswordFormValues = z.infer<typeof passwordFormSchema>

export function AccountForm() {
  const { user } = useAuthStore()
  const [isPasswordLoading, setIsPasswordLoading] = useState(false)

  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      old_password: '',
      new_password: '',
      confirm_password: '',
    },
  })



  // 修改密码
  async function onChangePassword(data: PasswordFormValues) {
    setIsPasswordLoading(true)

    try {
      const response = await AuthAPI.changePassword({
        old_password: data.old_password,
        new_password: data.new_password,
      })

      if (response.success) {
        toast.success('密码修改成功')
        passwordForm.reset()
      } else {
        toast.error(response.message || '密码修改失败')
      }
    } catch (error: any) {
      console.error('Change password error:', error)
      toast.error(error.response?.data?.message || '密码修改失败，请检查当前密码')
    } finally {
      setIsPasswordLoading(false)
    }
  }

  return (
    <div className='space-y-8'>
      {/* 账户信息部分 */}
      <div>
        <h3 className='text-lg font-medium'>账户信息</h3>
        <p className='text-sm text-muted-foreground'>
          查看您的账户信息。邮箱地址不可修改。
        </p>
        <Separator className='my-4' />

        <div className='space-y-4'>
          <div>
            <label className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
              邮箱地址
            </label>
            <div className='mt-2'>
              <Input
                value={user?.email || ''}
                disabled
                className='bg-muted cursor-not-allowed'
              />
            </div>
            <p className='text-sm text-muted-foreground mt-2'>
              这是您的登录邮箱地址，不可修改。如需更改邮箱，请联系管理员。
            </p>
          </div>
        </div>
      </div>

      {/* 修改密码部分 */}
      <div>
        <h3 className='text-lg font-medium'>修改密码</h3>
        <p className='text-sm text-muted-foreground'>
          更改您的账户密码。
        </p>
        <Separator className='my-4' />

        <Form {...passwordForm}>
          <form onSubmit={passwordForm.handleSubmit(onChangePassword)} className='space-y-4'>
            <FormField
              control={passwordForm.control}
              name='old_password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>当前密码</FormLabel>
                  <FormControl>
                    <PasswordInput placeholder='请输入当前密码' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={passwordForm.control}
              name='new_password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>新密码</FormLabel>
                  <FormControl>
                    <PasswordInput placeholder='请输入新密码' {...field} />
                  </FormControl>
                  <FormDescription>
                    密码至少需要8个字符。
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={passwordForm.control}
              name='confirm_password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>确认新密码</FormLabel>
                  <FormControl>
                    <PasswordInput placeholder='请再次输入新密码' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type='submit' disabled={isPasswordLoading}>
              {isPasswordLoading ? '修改中...' : '修改密码'}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  )
}
