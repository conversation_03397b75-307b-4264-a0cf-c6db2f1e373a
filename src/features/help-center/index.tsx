import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { AIAssistant } from '@/components/ai-assistant/ai-assistant'

export default function HelpCenterPage() {
  return (
    <DashboardLayout
      pageHeader={{
        title: '帮助中心',
        description: '查找问题答案并获得支持，或使用AI助手获得即时帮助。'
      }}
    >
      <div className="space-y-8">
        {/* AI助手区域 */}
        <div className="w-full">
          <AIAssistant className="w-full" />
        </div>

        {/* 分隔线 */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              或浏览帮助文档
            </span>
          </div>
        </div>

        {/* 传统帮助区域 */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold">我们如何帮助您？</h2>
            <p className="text-muted-foreground mt-2">
              浏览我们的帮助文章或联系支持团队获得帮助。
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
              <h3 className="font-semibold mb-2">快速入门</h3>
              <p className="text-sm text-muted-foreground">
                了解使用我们平台的基础知识。
              </p>
            </div>

            <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
              <h3 className="font-semibold mb-2">用户管理</h3>
              <p className="text-sm text-muted-foreground">
                如何管理用户和权限设置。
              </p>
            </div>

            <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
              <h3 className="font-semibold mb-2">MQTT配置</h3>
              <p className="text-sm text-muted-foreground">
                配置和管理MQTT连接。
              </p>
            </div>

            <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
              <h3 className="font-semibold mb-2">设备管理</h3>
              <p className="text-sm text-muted-foreground">
                管理连接的设备和设备类型。
              </p>
            </div>

            <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
              <h3 className="font-semibold mb-2">系统监控</h3>
              <p className="text-sm text-muted-foreground">
                监控系统状态和查看日志。
              </p>
            </div>

            <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
              <h3 className="font-semibold mb-2">故障排除</h3>
              <p className="text-sm text-muted-foreground">
                常见问题解决方案和故障排除指南。
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
