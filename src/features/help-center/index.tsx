import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default function HelpCenterPage() {
  return (
    <DashboardLayout
      pageHeader={{
        title: 'Help Center',
        description: 'Find answers to your questions and get support.'
      }}
    >
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold">How can we help you?</h2>
          <p className="text-muted-foreground mt-2">
            Browse our help articles or contact support for assistance.
          </p>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Getting Started</h3>
            <p className="text-sm text-muted-foreground">
              Learn the basics of using our platform.
            </p>
          </div>
          
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">User Management</h3>
            <p className="text-sm text-muted-foreground">
              How to manage users and permissions.
            </p>
          </div>
          
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">MQTT Configuration</h3>
            <p className="text-sm text-muted-foreground">
              Configure and manage MQTT connections.
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
