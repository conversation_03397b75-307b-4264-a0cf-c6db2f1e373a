import { useMutation, useQuery } from '@/hooks/use-api'
import { toast } from 'sonner'
import {
  UsersAPI,
  GetUsersParams,
  UpdateUserRequest,
  UpdateUserStatusRequest
} from '@/api/users'

export type { UpdateUserRequest }

// 查询键
const USERS_QUERY_KEY = 'users'

// 获取用户列表
export function useUsers(params?: GetUsersParams) {
  return useQuery({
    queryKey: [USERS_QUERY_KEY, params],
    queryFn: () => UsersAPI.getUsers(params),
    select: (data) => data.data,
  })
}

// 获取单个用户
export function useUser(id: string) {
  return useQuery({
    queryKey: [USERS_QUERY_KEY, id],
    queryFn: () => UsersAPI.getUser(id),
    select: (data) => data.data,
    enabled: !!id,
  })
}

// 更新用户
export function useUpdateUser(onSuccess?: () => void) {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserRequest }) =>
      UsersAPI.updateUser(id, data),
    onSuccess: (response) => {
      toast.success(response.message || '用户更新成功')
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '用户更新失败')
    },
  })
}

// 更新用户状态
export function useUpdateUserStatus(onSuccess?: () => void) {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserStatusRequest }) =>
      UsersAPI.updateUserStatus(id, data),
    onSuccess: (response) => {
      toast.success(response.message || '用户状态更新成功')
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '用户状态更新失败')
    },
  })
}

// 删除用户
export function useDeleteUser(onSuccess?: () => void) {
  return useMutation({
    mutationFn: (id: string) => UsersAPI.deleteUser(id),
    onSuccess: (response) => {
      toast.success(response.message || '用户删除成功')
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '用户删除失败')
    },
  })
}

// 批量操作用户状态
export function useBatchUpdateUserStatus(onSuccess?: () => void) {
  return useMutation({
    mutationFn: async ({ userIds, status }: { userIds: string[]; status: UpdateUserStatusRequest['status'] }) => {
      const promises = userIds.map(id =>
        UsersAPI.updateUserStatus(id, { status })
      )
      return Promise.all(promises)
    },
    onSuccess: (responses) => {
      const successCount = responses.filter(r => r.success).length
      toast.success(`成功更新 ${successCount} 个用户状态`)
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || '批量更新用户状态失败')
    },
  })
}