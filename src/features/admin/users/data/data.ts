import {
  IconUserShield,
  IconUser,
} from '@tabler/icons-react'
import { UserStatus } from './schema'

export const statusStyles = new Map<UserStatus, string>([
  ['active', 'bg-teal-100/30 text-teal-900 dark:text-teal-200 border-teal-200'],
  ['inactive', 'bg-neutral-300/40 border-neutral-300'],
  ['invited', 'bg-sky-200/40 text-sky-900 dark:text-sky-100 border-sky-300'],
  [
    'suspended',
    'bg-destructive/10 dark:bg-destructive/50 text-destructive dark:text-primary border-destructive/10',
  ],
  [
    'banned',
    'bg-red-500/20 text-red-700 dark:text-red-400 border-red-500/30',
  ],
  [
    'pending',
    'bg-amber-500/20 text-amber-700 dark:text-amber-400 border-amber-500/30',
  ],
])

export const statusLabels = new Map<UserStatus, string>([
  ['active', 'Active'],
  ['inactive', 'Inactive'],
  ['banned', 'Banned'],
  ['pending', 'Pending'],
])

export const userTypes = [
  {
    label: 'Admin',
    value: 'admin',
    icon: IconUserShield,
  },
  {
    label: 'User',
    value: 'user',
    icon: IconUser,
  },
] as const

export const roleOptions = userTypes.map(({ label, value }) => ({
  label,
  value,
}))

export const statusOptions = Array.from(statusLabels, ([value, label]) => ({
  value,
  label,
}))