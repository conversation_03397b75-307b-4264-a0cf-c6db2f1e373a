import { z } from 'zod'

export const userStatusSchema = z.union([
  z.literal('active'),
  z.literal('inactive'),
  z.literal('invited'),
  z.literal('suspended'),
  z.literal('banned'),
  z.literal('pending'),
])
export type UserStatus = z.infer<typeof userStatusSchema>

export const userRoleSchema = z.union([
  z.literal('admin'),
  z.literal('user'),
])
export type UserRole = z.infer<typeof userRoleSchema>

const userProfileSchema = z.object({
  first_name: z.string().nullable().optional(),
  last_name: z.string().nullable().optional(),
  display_name: z.string().nullable().optional(),
  company: z.string().nullable().optional(),
  department: z.string().nullable().optional(),
  avatar: z.string().nullable().optional(),
})

const userSchema = z.object({
  id: z.string(),
  username: z.string(),
  email: z.string(),
  phoneNumber: z.string().optional(),
  status: userStatusSchema,
  role: userRoleSchema,
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  profile: userProfileSchema.optional(),
})
export type User = z.infer<typeof userSchema>

export const userListSchema = z.array(userSchema)