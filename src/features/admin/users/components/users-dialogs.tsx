import { useUsers } from '../context/users-context'
import { UsersActionDialog } from './users-action-dialog'
import { UsersDeleteDialog } from './users-delete-dialog'

interface UsersDialogsProps {
  onDataRefresh?: () => void
}

export function UsersDialogs({ onDataRefresh }: UsersDialogsProps) {
  const { open, setOpen, currentRow, setCurrentRow } = useUsers()

  const handleDialogClose = () => {
    setOpen(null)
    setTimeout(() => {
      setCurrentRow(null)
    }, 500)
  }

  return (
    <>
      <UsersActionDialog
        open={open === 'add'}
        onOpenChange={handleDialogClose}
        onDataRefresh={onDataRefresh}
      />
      <UsersActionDialog
        open={open === 'edit'}
        onOpenChange={handleDialogClose}
        currentRow={currentRow || undefined}
        onDataRefresh={onDataRefresh}
      />
      <UsersDeleteDialog
        open={open === 'delete'}
        onOpenChange={handleDialogClose}
        currentRow={currentRow || undefined}
        onDataRefresh={onDataRefresh}
      />
    </>
  )
}
