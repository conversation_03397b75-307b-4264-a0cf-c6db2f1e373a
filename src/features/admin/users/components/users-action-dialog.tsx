'use client'

import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { SelectDropdown } from '@/components/custom_components/select-dropdown'
import {
  statusOptions,
  roleOptions,
} from '@/features/admin/users/data/data'
import {
  User,
  userRoleSchema,
  userStatusSchema,
} from '@/features/admin/users/data/schema'
import { useUpdateUser, UpdateUserRequest } from '../hooks/use-users'

// 表单模式 - 匹配API要求
const formSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required.' })
    .email({ message: 'Email is invalid.' }),
  role: userRoleSchema,
  status: userStatusSchema,
  profile: z
    .object({
      first_name: z.string().optional(),
      last_name: z.string().optional(),
      display_name: z.string().optional(),
      avatar: z.string().optional(),
      company: z.string().optional(),
      department: z.string().optional(),
    })
    .optional(),
})

type UserForm = z.infer<typeof formSchema>

interface Props {
  currentRow?: User
  open: boolean
  onOpenChange: (open: boolean) => void
  onDataRefresh?: () => void
}

export function UsersActionDialog({ currentRow, open, onOpenChange, onDataRefresh }: Props) {
  const isEdit = !!currentRow
  const updateUserMutation = useUpdateUser(() => {
    onDataRefresh?.()
  })

  const form = useForm<UserForm>({
    resolver: zodResolver(formSchema),
    defaultValues:
      isEdit && currentRow
        ? {
            email: currentRow.email,
            role: currentRow.role,
            status: currentRow.status,
            profile: {
              first_name: currentRow.profile?.first_name || '',
              last_name: currentRow.profile?.last_name || '',
              display_name: currentRow.profile?.display_name || '',
              avatar: currentRow.profile?.avatar || '',
              company: currentRow.profile?.company || '',
              department: currentRow.profile?.department || '',
            },
          }
        : {
            email: '',
            role: 'user',
            status: 'active',
            profile: {
              first_name: '',
              last_name: '',
              display_name: '',
              avatar: '',
              company: '',
              department: '',
            },
          },
  })

  const onSubmit = async (values: UserForm) => {
    if (!isEdit || !currentRow) {
      // 新增用户功能暂未实现
      return
    }

    const updateData: UpdateUserRequest = {
      email: values.email,
      role: values.role,
      status: values.status,
      profile: values.profile,
    }

    try {
      await updateUserMutation.mutateAsync({
        id: currentRow.id,
        data: updateData,
      })
      form.reset()
      onOpenChange(false)
    } catch (error) {
      // 错误已在hook中处理
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (!state) {
          form.reset()
        }
        onOpenChange(state)
      }}
    >
      <DialogContent className='sm:max-w-lg'>
        <DialogHeader className='text-left'>
          <DialogTitle>{isEdit ? 'Edit User' : 'Add New User'}</DialogTitle>
          <DialogDescription>
            {isEdit ? 'Update the user here. ' : 'Create new user here. '}
            Click save when you&apos;re done.
          </DialogDescription>
        </DialogHeader>
        <div className='-mr-4 h-[26.25rem] w-full overflow-y-auto py-1 pr-4'>
          <Form {...form}>
            <form
              id='user-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='space-y-4 p-0.5'
            >
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                    <FormLabel className='col-span-2 text-right'>
                      Email
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='<EMAIL>'
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='role'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                    <FormLabel className='col-span-2 text-right'>
                      Role
                    </FormLabel>
                    <div className='col-span-4'>
                      <FormControl>
                        <SelectDropdown
                          items={roleOptions}
                          placeholder='Select role'
                          value={field.value}
                          onValueChange={field.onChange}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='status'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                    <FormLabel className='col-span-2 text-right'>
                      Status
                    </FormLabel>
                    <div className='col-span-4'>
                      <FormControl>
                        <SelectDropdown
                          items={statusOptions}
                          placeholder='Select status'
                          value={field.value}
                          onValueChange={field.onChange}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              {/* Profile Fields */}
              <div className='space-y-4 border-t pt-4'>
                <h4 className='text-sm font-medium'>Profile Information</h4>

                <FormField
                  control={form.control}
                  name='profile.first_name'
                  render={({ field }) => (
                    <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                      <FormLabel className='col-span-2 text-right'>
                        First Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Beacon'
                          className='col-span-4'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className='col-span-4 col-start-3' />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='profile.last_name'
                  render={({ field }) => (
                    <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                      <FormLabel className='col-span-2 text-right'>
                        Last Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Doe'
                          className='col-span-4'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className='col-span-4 col-start-3' />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='profile.display_name'
                  render={({ field }) => (
                    <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                      <FormLabel className='col-span-2 text-right'>
                        Display Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Beacon Tech'
                          className='col-span-4'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className='col-span-4 col-start-3' />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='profile.company'
                  render={({ field }) => (
                    <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                      <FormLabel className='col-span-2 text-right'>
                        Company
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Acme Corp'
                          className='col-span-4'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className='col-span-4 col-start-3' />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='profile.department'
                  render={({ field }) => (
                    <FormItem className='grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1'>
                      <FormLabel className='col-span-2 text-right'>
                        Department
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Engineering'
                          className='col-span-4'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className='col-span-4 col-start-3' />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>
        <DialogFooter>
          <Button
            type='submit'
            form='user-form'
            disabled={updateUserMutation.isPending}
          >
            {updateUserMutation.isPending ? 'Saving...' : 'Save changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
