'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/custom_components/confirm-dialog'
import { User } from '../data/schema'
import { useDeleteUser } from '../hooks/use-users'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: User
  onDataRefresh?: () => void
}

export function UsersDeleteDialog({ open, onOpenChange, currentRow, onDataRefresh }: Props) {
  const [value, setValue] = useState('')
  const deleteUserMutation = useDeleteUser(() => {
    onDataRefresh?.()
  })

  if (!currentRow) {
    return null
  }

  const userDisplayName = currentRow.profile?.display_name ||
                          `${currentRow.profile?.first_name || ''} ${currentRow.profile?.last_name || ''}`.trim() ||
                          currentRow.email

  const handleDelete = async () => {
    if (value.trim() !== currentRow.email) return

    try {
      await deleteUserMutation.mutateAsync(currentRow.id)
      onOpenChange(false)
      setValue('')
    } catch (error) {
      // 错误已在hook中处理
    }
  }

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={(state) => {
        onOpenChange(state)
        if (!state) setValue('')
      }}
      handleConfirm={handleDelete}
      disabled={value.trim() !== currentRow.email || deleteUserMutation.isPending}
      title={
        <span className='text-destructive'>
          <IconAlertTriangle
            className='stroke-destructive mr-1 inline-block'
            size={18}
          />{' '}
          Delete User
        </span>
      }
      desc={
        <div className='space-y-4'>
          <p className='mb-2'>
            Are you sure you want to delete{' '}
            <span className='font-bold'>{userDisplayName}</span>?
            <br />
            This action will permanently remove the user with the role of{' '}
            <span className='font-bold'>
              {currentRow.role.toUpperCase()}
            </span>{' '}
            from the system. This cannot be undone.
          </p>

          <Label className='my-2'>
            Email:
            <Input
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder='Enter email to confirm deletion.'
              disabled={deleteUserMutation.isPending}
            />
          </Label>

          <Alert variant='destructive'>
            <AlertTitle>Warning!</AlertTitle>
            <AlertDescription>
              Please be careful, this operation cannot be rolled back.
            </AlertDescription>
          </Alert>
        </div>
      }
      confirmText={deleteUserMutation.isPending ? 'Deleting...' : 'Delete'}
      destructive
    />
  )
}
