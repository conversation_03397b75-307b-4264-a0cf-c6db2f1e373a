import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { useDevices } from '../context/devices-context'

export function DevicesPrimaryButtons() {
  const { openDialog } = useDevices()

  return (
    <div className="flex items-center space-x-2">
      <Button onClick={() => openDialog('add')}>
        <Plus className="mr-2 h-4 w-4" />
        添加设备
      </Button>
    </div>
  )
}
