import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Device } from '../data/schema'
import { useDeleteDevice } from '../hooks/use-devices'
import { useDevices } from '../context/devices-context'

interface DevicesDeleteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Device
  onDataRefresh?: () => void
}

export function DevicesDeleteDialog({
  open,
  onOpenChange,
  currentRow,
  onDataRefresh,
}: DevicesDeleteDialogProps) {
  const { closeDialog } = useDevices()
  const deleteDevice = useDeleteDevice(() => {
    onDataRefresh?.()
  })
  const device = currentRow

  const handleDelete = async () => {
    if (!device) return

    try {
      await deleteDevice.mutateAsync(device.id)
      // 立即关闭对话框
      closeDialog()
    } catch (error) {
      // 错误处理已在hooks中完成
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除设备</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除设备 "{device?.device_name}" 吗？
            <br />
            <span className="text-sm text-muted-foreground">
              设备类型：{device?.device_type} | 序列号：{device?.serial_number}
            </span>
            <br />
            <br />
            <span className="text-red-600 font-medium">
              此操作无法撤销，设备的所有相关数据将被永久删除。
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteDevice.isPending}>
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteDevice.isPending}
            className="bg-red-600 hover:bg-red-700"
          >
            {deleteDevice.isPending ? '删除中...' : '确认删除'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
