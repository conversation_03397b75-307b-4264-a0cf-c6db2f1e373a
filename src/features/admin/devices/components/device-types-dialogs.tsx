import { useDeviceTypes } from '../context/device-types-context'
import { DeviceTypesActionDialog } from './device-types-action-dialog'
import { DeviceTypesDeleteDialog } from './device-types-delete-dialog'

interface DeviceTypesDialogsProps {
  onDataRefresh?: () => void
}

export function DeviceTypesDialogs({ onDataRefresh }: DeviceTypesDialogsProps) {
  const { open, setOpen, currentRow, setCurrentRow } = useDeviceTypes()

  const handleDialogClose = () => {
    setOpen(null)
    setTimeout(() => {
      setCurrentRow(null)
    }, 500)
  }

  return (
    <>
      <DeviceTypesActionDialog
        open={open === 'add'}
        onOpenChange={handleDialogClose}
        onDataRefresh={onDataRefresh}
      />
      <DeviceTypesActionDialog
        open={open === 'edit'}
        onOpenChange={handleDialogClose}
        currentRow={currentRow || undefined}
        onDataRefresh={onDataRefresh}
      />
      <DeviceTypesDeleteDialog
        open={open === 'delete'}
        onOpenChange={handleDialogClose}
        currentRow={currentRow || undefined}
        onDataRefresh={onDataRefresh}
      />
    </>
  )
}
