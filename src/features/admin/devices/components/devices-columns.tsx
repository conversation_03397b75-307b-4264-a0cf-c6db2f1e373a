import { DataTableColumn } from '@/components/data-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2 } from 'lucide-react'
import { format } from 'date-fns'
import { Device } from '../data/schema'
import { statusStyles } from '../data/data'
import { useDevices } from '../context/devices-context'

export const columns: DataTableColumn<Device>[] = [
  {
    id: 'device_name',
    accessorKey: 'device_name',
    header: '设备名称',
    cell: (device: Device) => {
      return (
        <div className="flex flex-col">
          <span className="font-medium">{device.device_name}</span>
          <span className="text-sm text-muted-foreground">{device.device_type}</span>
        </div>
      )
    },
  },
  {
    id: 'device_type',
    accessorKey: 'device_type',
    header: '设备类型',
    cell: (device: Device) => {
      return (
        <span className="font-medium">{device.device_type}</span>
      )
    },
  },
  {
    id: 'serial_number',
    accessorKey: 'serial_number',
    header: '序列号',
    cell: (device: Device) => {
      return (
        <span className="font-mono text-sm">{device.serial_number}</span>
      )
    },
  },
  {
    id: 'imei_code',
    accessorKey: 'imei_code',
    header: 'IMEI码',
    cell: (device: Device) => {
      return (
        <span className="font-mono text-sm">{device.imei_code}</span>
      )
    },
  },
  {
    id: 'status',
    accessorKey: 'status',
    header: '状态',
    cell: (device: Device) => {
      const status = device.status as keyof typeof statusStyles
      const statusConfig = statusStyles[status]

      return (
        <Badge variant="outline" className={statusConfig.className}>
          {statusConfig.label}
        </Badge>
      )
    },
  },
  {
    id: 'created_at',
    accessorKey: 'created_at',
    header: '创建时间',
    cell: (device: Device) => {
      const date = new Date(device.created_at)
      return (
        <div className="flex flex-col">
          <span className="text-sm">{format(date, 'yyyy-MM-dd')}</span>
          <span className="text-xs text-muted-foreground">{format(date, 'HH:mm:ss')}</span>
        </div>
      )
    },
  },
  {
    id: 'actions',
    header: '操作',
    cell: (device: Device) => {
      const { openDialog } = useDevices()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">打开菜单</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => openDialog('edit', device)}
              className="cursor-pointer"
            >
              <Edit className="mr-2 h-4 w-4" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => openDialog('delete', device)}
              className="cursor-pointer text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
