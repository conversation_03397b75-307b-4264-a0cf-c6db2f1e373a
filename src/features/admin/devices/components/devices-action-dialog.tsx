import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Device, DeviceFormData, deviceFormSchema } from '../data/schema'
import { deviceStatuses } from '../data/data'
import { useCreateDevice, useUpdateDevice } from '../hooks/use-devices'
import { useDevices } from '../context/devices-context'

interface DevicesActionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Device
  deviceTypes?: Array<{ id: string; name: string }>
  onDataRefresh?: () => void
}

export function DevicesActionDialog({
  open,
  onOpenChange,
  currentRow,
  deviceTypes = [],
  onDataRefresh,
}: DevicesActionDialogProps) {
  // 根据是否有currentRow来判断是添加还是编辑模式
  const mode = currentRow ? 'edit' : 'add'
  const device = currentRow
  const { closeDialog } = useDevices()
  const createDevice = useCreateDevice(() => {
    onDataRefresh?.()
  })
  const updateDevice = useUpdateDevice(() => {
    onDataRefresh?.()
  })

  // 使用传入的设备类型数据

  const form = useForm<DeviceFormData>({
    resolver: zodResolver(deviceFormSchema),
    defaultValues: {
      device_type: '',
      device_name: '',
      serial_number: '',
      imei_code: '',
      status: 'active',
    },
  })

  // 当设备数据变化时更新表单
  useEffect(() => {
    if (mode === 'edit' && device) {
      form.reset({
        device_type: device.device_type,
        device_name: device.device_name,
        serial_number: device.serial_number,
        imei_code: device.imei_code,
        status: device.status,
      })
    } else if (mode === 'add') {
      form.reset({
        device_type: '',
        device_name: '',
        serial_number: '',
        imei_code: '',
        status: 'active',
      })
    }
  }, [mode, device, form])

  const onSubmit = async (data: DeviceFormData) => {
    try {
      if (mode === 'add') {
        await createDevice.mutateAsync({
          device_type: data.device_type,
          device_name: data.device_name,
          serial_number: data.serial_number,
          imei_code: data.imei_code,
        })
      } else if (mode === 'edit' && device) {
        await updateDevice.mutateAsync({
          id: device.id,
          data: {
            device_name: data.device_name,
            status: data.status,
          },
        })
      }
      // 立即关闭对话框
      closeDialog()
    } catch (error) {
      // 错误处理已在hooks中完成
    }
  }

  const isLoading = createDevice.isPending || updateDevice.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'add' ? '添加设备' : '编辑设备'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'add' 
              ? '填写设备信息以添加新设备。' 
              : '修改设备信息。注意：设备类型、序列号和IMEI码不可修改。'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="device_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>设备类型</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                    disabled={mode === 'edit'}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择设备类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {deviceTypes.map((type) => (
                        <SelectItem key={type.id} value={type.name}>
                          <span>{type.name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="device_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>设备名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入设备名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="serial_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>序列号</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="输入序列号" 
                      {...field} 
                      disabled={mode === 'edit'}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="imei_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>IMEI码</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="输入IMEI码" 
                      {...field} 
                      disabled={mode === 'edit'}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {mode === 'edit' && (
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>状态</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择设备状态" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {deviceStatuses.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            <div className="flex items-center space-x-2">
                              <status.icon className={`h-4 w-4 ${status.color}`} />
                              <span>{status.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => closeDialog()}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? '保存中...' : mode === 'add' ? '添加' : '保存'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
