import { useDevices } from '../context/devices-context'
import { DevicesActionDialog } from './devices-action-dialog'
import { DevicesDeleteDialog } from './devices-delete-dialog'
import { useAllDeviceTypes } from '../hooks/use-device-types'

interface DevicesDialogsProps {
  onDataRefresh?: () => void
}

export function DevicesDialogs({ onDataRefresh }: DevicesDialogsProps) {
  const { open, setOpen, currentRow, setCurrentRow } = useDevices()
  const { data: deviceTypesResponse } = useAllDeviceTypes()

  const handleDialogClose = () => {
    setOpen(null)
    setTimeout(() => {
      setCurrentRow(null)
    }, 500)
  }

  const deviceTypes = deviceTypesResponse?.device_types || []

  return (
    <>
      <DevicesActionDialog
        open={open === 'add'}
        onOpenChange={handleDialogClose}
        deviceTypes={deviceTypes}
        onDataRefresh={onDataRefresh}
      />
      <DevicesActionDialog
        open={open === 'edit'}
        onOpenChange={handleDialogClose}
        currentRow={currentRow || undefined}
        deviceTypes={deviceTypes}
        onDataRefresh={onDataRefresh}
      />
      <DevicesDeleteDialog
        open={open === 'delete'}
        onOpenChange={handleDialogClose}
        currentRow={currentRow || undefined}
        onDataRefresh={onDataRefresh}
      />
    </>
  )
}
