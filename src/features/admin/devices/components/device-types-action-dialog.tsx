import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { DeviceType, DeviceTypeFormData, deviceTypeFormSchema } from '../data/schema'
import { useCreateDeviceType, useUpdateDeviceType } from '../hooks/use-device-types'
import { useDeviceTypes } from '../context/device-types-context'

interface DeviceTypesActionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: DeviceType
  onDataRefresh?: () => void
}

export function DeviceTypesActionDialog({
  open,
  onOpenChange,
  currentRow,
  onDataRefresh,
}: DeviceTypesActionDialogProps) {
  const { closeDialog } = useDeviceTypes()
  const createDeviceType = useCreateDeviceType(() => {
    onDataRefresh?.()
  })
  const updateDeviceType = useUpdateDeviceType(() => {
    onDataRefresh?.()
  })

  // 根据是否有currentRow来判断是添加还是编辑模式
  const mode = currentRow ? 'edit' : 'add'
  const deviceType = currentRow

  const form = useForm<DeviceTypeFormData>({
    resolver: zodResolver(deviceTypeFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  })

  // 当设备类型数据变化时更新表单
  useEffect(() => {
    if (mode === 'edit' && deviceType) {
      form.reset({
        name: deviceType.name,
        description: deviceType.description,
      })
    } else if (mode === 'add') {
      form.reset({
        name: '',
        description: '',
      })
    }
  }, [mode, deviceType, form])

  const onSubmit = async (data: DeviceTypeFormData) => {
    try {
      if (mode === 'add') {
        await createDeviceType.mutateAsync(data)
      } else if (mode === 'edit' && deviceType) {
        await updateDeviceType.mutateAsync({
          id: deviceType.id,
          data,
        })
      }
      // 立即关闭对话框
      closeDialog()
    } catch (error) {
      // 错误处理已在hooks中完成
    }
  }

  const isLoading = createDeviceType.isPending || updateDeviceType.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'add' ? '添加设备类型' : '编辑设备类型'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'add' 
              ? '填写设备类型信息以添加新的设备类型。' 
              : '修改设备类型信息。'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>设备类型名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入设备类型名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="输入设备类型描述"
                      className="resize-none"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => closeDialog()}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? '保存中...' : mode === 'add' ? '添加' : '保存'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
