import { useMemo } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DataTable } from '@/components/data-table'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { columns } from './components/device-types-columns'
import { DeviceTypesDialogs } from './components/device-types-dialogs'
import DeviceTypesProvider from './context/device-types-context'
import { useAllDeviceTypes } from './hooks/use-device-types'
import { useDeviceTypes } from './context/device-types-context'

function DeviceTypesPrimaryButtons() {
  const { openDialog } = useDeviceTypes()

  return (
    <div className="flex items-center space-x-2">
      <Button onClick={() => openDialog('add')}>
        <Plus className="mr-2 h-4 w-4" />
        添加设备类型
      </Button>
    </div>
  )
}

function DeviceTypesPageContent() {
  // 获取所有设备类型数据（不分页）
  const { data: deviceTypesResponse, isLoading, error, refetch } = useAllDeviceTypes()

  // 转换设备类型数据
  const transformedDeviceTypes = useMemo(() => {
    return deviceTypesResponse?.device_types || []
  }, [deviceTypesResponse])

  return (
    <DashboardLayout
      headerFixed={true}
      pageHeader={{
        title: '设备类型管理',
        description: '管理系统中的设备类型，包括预定义类型和自定义类型。',
        actions: <DeviceTypesPrimaryButtons />
      }}
    >
      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
        <DataTable
          columns={columns}
          data={transformedDeviceTypes}
          isLoading={isLoading}
          emptyMessage="暂无设备类型数据。"
          searchPlaceholder="搜索设备类型名称或描述..."
          searchFields={['name', 'description']}
          showPagination={false}
        />
      </div>

      <DeviceTypesDialogs onDataRefresh={refetch} />
    </DashboardLayout>
  )
}

export default function DeviceTypesPage() {
  return (
    <DeviceTypesProvider>
      <DeviceTypesPageContent />
    </DeviceTypesProvider>
  )
}
