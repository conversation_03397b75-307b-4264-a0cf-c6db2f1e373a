import { useMutation, useQuery } from '@/hooks/use-api'
import { toast } from 'sonner'
import {
  DevicesAPI,
  GetDevicesParams,
  CreateDeviceRequest,
  UpdateDeviceRequest
} from '@/api/devices'
import { DEVICE_TYPES_QUERY_KEY } from './use-device-types'

export type { CreateDeviceRequest, UpdateDeviceRequest }

// 查询键
const DEVICES_QUERY_KEY = 'devices'
export { DEVICES_QUERY_KEY }

// 获取设备列表
export function useDevices(params?: GetDevicesParams) {
  return useQuery({
    queryKey: [DEVICES_QUERY_KEY, params],
    queryFn: () => DevicesAPI.getDevices(params),
    select: (data) => data.data,
  })
}

// 获取单个设备
export function useDevice(id: string) {
  return useQuery({
    queryKey: [DEVICES_QUERY_KEY, id],
    queryFn: () => DevicesAPI.getDevice(id),
    select: (data) => data.data,
    enabled: !!id,
  })
}

// 创建设备
export function useCreateDevice(onSuccess?: () => void) {
  return useMutation({
    mutationFn: (data: CreateDeviceRequest) => DevicesAPI.createDevice(data),
    onSuccess: (response) => {
      toast.success(response.message || '设备创建成功')
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || '创建设备失败'
      toast.error(message)
    },
  })
}

// 更新设备
export function useUpdateDevice(onSuccess?: () => void) {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateDeviceRequest }) =>
      DevicesAPI.updateDevice(id, data),
    onSuccess: (response) => {
      toast.success(response.message || '设备更新成功')
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || '更新设备失败'
      toast.error(message)
    },
  })
}

// 删除设备
export function useDeleteDevice(onSuccess?: () => void) {
  return useMutation({
    mutationFn: (id: string) => DevicesAPI.deleteDevice(id),
    onSuccess: (response) => {
      toast.success(response.message || '设备删除成功')
      // 调用外部传入的成功回调来刷新数据
      onSuccess?.()
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || '删除设备失败'
      toast.error(message)
    },
  })
}
