import {
  CheckCircle,
  XCircle,
  AlertTriangle,
} from 'lucide-react'
import { DeviceStatus } from './schema'

// 设备状态配置
export const deviceStatuses = [
  {
    value: 'active' as DeviceStatus,
    label: '正常',
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200',
  },
  {
    value: 'inactive' as DeviceStatus,
    label: '停用',
    icon: XCircle,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-200',
  },
  {
    value: 'maintenance' as DeviceStatus,
    label: '维护',
    icon: AlertTriangle,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-200',
  },
]

// 状态样式映射
export const statusStyles = {
  active: {
    label: '正常',
    className: 'bg-green-100 text-green-800 border-green-200',
  },
  inactive: {
    label: '停用',
    className: 'bg-gray-100 text-gray-800 border-gray-200',
  },
  maintenance: {
    label: '维护',
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
}

// 状态标签映射
export const statusLabels: Record<DeviceStatus, string> = {
  active: '正常',
  inactive: '停用',
  maintenance: '维护',
}
