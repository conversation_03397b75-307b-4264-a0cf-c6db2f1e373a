import { z } from 'zod'

// 设备状态枚举
export type DeviceStatus = 'active' | 'inactive' | 'maintenance'

// 设备接口
export interface Device {
  id: string
  device_type: string
  device_name: string
  serial_number: string
  imei_code: string
  status: DeviceStatus
  created_at: string
  updated_at: string
}

// 设备类型接口
export interface DeviceType {
  id: string
  name: string
  description: string
  created_at: string
  updated_at: string
}

// 设备表单验证schema
export const deviceFormSchema = z.object({
  device_type: z.string().min(1, '请选择设备类型'),
  device_name: z.string().min(1, '请输入设备名称').max(100, '设备名称不能超过100个字符'),
  serial_number: z.string().min(1, '请输入序列号').max(50, '序列号不能超过50个字符'),
  imei_code: z.string().min(1, '请输入IMEI码').max(50, 'IMEI码不能超过50个字符'),
  status: z.enum(['active', 'inactive', 'maintenance']).optional(),
})

// 设备类型表单验证schema
export const deviceTypeFormSchema = z.object({
  name: z.string().min(1, '请输入设备类型名称').max(50, '设备类型名称不能超过50个字符'),
  description: z.string().min(1, '请输入设备类型描述').max(200, '描述不能超过200个字符'),
})

// 设备表单数据类型
export type DeviceFormData = z.infer<typeof deviceFormSchema>

// 设备类型表单数据类型
export type DeviceTypeFormData = z.infer<typeof deviceTypeFormSchema>
