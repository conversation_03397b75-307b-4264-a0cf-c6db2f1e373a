import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { IconCpu, IconDatabase, IconNetwork, IconDeviceDesktop } from '@tabler/icons-react'
import type { DashboardData, ServerInfo } from '@/types/monitor'

interface SystemMetricsProps {
  systemData: DashboardData['system_metrics']
  serverInfo: ServerInfo
}

export function SystemMetrics({ systemData, serverInfo }: SystemMetricsProps) {
  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    if (i >= sizes.length) {
      return (bytes / Math.pow(1024, sizes.length - 1)).toFixed(1) + ' ' + sizes[sizes.length - 1]
    }
    return (bytes / Math.pow(1024, i)).toFixed(1) + ' ' + sizes[i]
  }



  return (
    <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
      {/* System Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>System Metrics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* CPU Usage */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <IconCpu className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">CPU Usage</span>
              <span className="ml-auto text-sm">{systemData.cpu.usage.toFixed(1)}%</span>
            </div>
            <Progress
              value={systemData.cpu.usage}
              className="h-2"
            />
            <div className="text-xs text-muted-foreground">
              Load Avg: {systemData.cpu.load_avg_1.toFixed(2)} / {systemData.cpu.load_avg_5.toFixed(2)} / {systemData.cpu.load_avg_15.toFixed(2)}
            </div>
          </div>

          {/* Memory Usage */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <IconDatabase className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Memory Usage</span>
              <span className="ml-auto text-sm">{systemData.memory.usage.toFixed(1)}%</span>
            </div>
            <Progress
              value={systemData.memory.usage}
              className="h-2"
            />
            <div className="text-xs text-muted-foreground">
              {formatBytes(systemData.memory.used)} / {formatBytes(systemData.memory.total)}
              (Available: {formatBytes(systemData.memory.available)})
            </div>
          </div>

          {/* Disk Usage */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <IconDeviceDesktop className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium">Disk Usage</span>
              <span className="ml-auto text-sm">{systemData.disk.usage.toFixed(1)}%</span>
            </div>
            <Progress
              value={systemData.disk.usage}
              className="h-2"
            />
            <div className="text-xs text-muted-foreground">
              {formatBytes(systemData.disk.used)} / {formatBytes(systemData.disk.total)}
              (Available: {formatBytes(systemData.disk.available)})
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Network & MQTT Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Network & MQTT Metrics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">

          {/* System Network Stats */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">System Network</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <IconNetwork className="h-3 w-3 text-blue-500" />
                  <span className="text-xs font-medium">Bytes In</span>
                </div>
                <div className="text-sm font-semibold">
                  {formatBytes(systemData.network.bytes_in)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {systemData.network.packets_in.toLocaleString()} packets
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <IconNetwork className="h-3 w-3 text-green-500" />
                  <span className="text-xs font-medium">Bytes Out</span>
                </div>
                <div className="text-sm font-semibold">
                  {formatBytes(systemData.network.bytes_out)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {systemData.network.packets_out.toLocaleString()} packets
                </div>
              </div>
            </div>
          </div>

          {/* MQTT Network Stats */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">MQTT Network</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <IconNetwork className="h-3 w-3 text-purple-500" />
                  <span className="text-xs font-medium">MQTT In</span>
                </div>
                <div className="text-sm font-semibold">
                  {formatBytes(serverInfo.bytes_received)}
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <IconNetwork className="h-3 w-3 text-orange-500" />
                  <span className="text-xs font-medium">MQTT Out</span>
                </div>
                <div className="text-sm font-semibold">
                  {formatBytes(serverInfo.bytes_sent)}
                </div>
              </div>
            </div>
          </div>

          {/* MQTT Memory Usage */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">MQTT Memory</h4>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <IconDatabase className="h-3 w-3 text-indigo-500" />
                <span className="text-xs font-medium">MQTT Memory Usage</span>
              </div>
              <div className="text-sm font-semibold">
                {formatBytes(serverInfo.memory_alloc)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
