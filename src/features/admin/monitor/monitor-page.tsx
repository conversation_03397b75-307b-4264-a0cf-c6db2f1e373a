import { useQuery } from '@/hooks/use-api'
import { 
  IconUsers, 
  IconMessage, 
  IconActivity, 
  IconRefresh,
  IconDeviceDesktop 
} from '@tabler/icons-react'
import { DashboardLayout } from '@/components/layout'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { getMonitorDashboard, monitorQueryKeys } from '@/api/monitor'
import { StatsCard } from './components/stats-card'
import { SystemMetrics } from './components/performance-metrics'

export function MonitorPage() {

  const {
    data: dashboardData,
    isLoading: isDashboardLoading,
    error: dashboardError,
    refetch: refetchDashboard,
  } = useQuery({
    queryKey: monitorQueryKeys.dashboard(),
    queryFn: getMonitorDashboard,
    // refetchInterval: 30000, // 30秒自动刷新 - 不支持自动刷新
  })

  const handleRefresh = () => {
    refetchDashboard()
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    if (i >= sizes.length) {
      return (bytes / Math.pow(1024, sizes.length - 1)).toFixed(1) + ' ' + sizes[sizes.length - 1]
    }
    return (bytes / Math.pow(1024, i)).toFixed(1) + ' ' + sizes[i]
  }

  const breadcrumbItems = [
    { title: 'Admin', href: '/admin' },
    { title: 'Monitor', isActive: true },
  ]

  if (dashboardError) {
    return (
      <DashboardLayout
        breadcrumbItems={breadcrumbItems}
        pageHeader={{
          title: "Monitor Dashboard",
          description: "Real-time system monitoring and performance metrics"
        }}
      >
        <Alert variant="destructive">
          <AlertDescription>
            Failed to load monitoring data. Please try again later.
          </AlertDescription>
        </Alert>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      breadcrumbItems={breadcrumbItems}
      pageHeader={{
        title: "Monitor Dashboard",
        description: "Real-time system monitoring and performance metrics",
        actions: (
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <IconRefresh className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )
      }}
    >
      <div className="space-y-6">
        {/* Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {isDashboardLoading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))
          ) : dashboardData ? (
            <>
              <StatsCard
                title="Total Clients"
                value={formatNumber(dashboardData.server_info.clients_total)}
                description={`${dashboardData.server_info.clients_connected} active connections`}
                icon={IconUsers}
              />
              <StatsCard
                title="Total Messages"
                value={formatNumber(dashboardData.server_info.messages_received + dashboardData.server_info.messages_sent)}
                description={`Received: ${formatNumber(dashboardData.server_info.messages_received)} / Sent: ${formatNumber(dashboardData.server_info.messages_sent)}`}
                icon={IconMessage}
              />
              <StatsCard
                title="Max Connections"
                value={formatNumber(dashboardData.server_info.clients_maximum)}
                description={`Current: ${dashboardData.server_info.clients_connected}`}
                icon={IconActivity}
              />
              <StatsCard
                title="System Uptime"
                value={`${(dashboardData.server_info.uptime / 3600).toFixed(1)}h`}
                description={`Load: ${dashboardData.system_metrics.cpu.load_avg_1.toFixed(2)}`}
                icon={IconDeviceDesktop}
              />
            </>
          ) : null}
        </div>

        {/* System Resource Overview */}
        {dashboardData && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="CPU Usage"
              value={`${dashboardData.system_metrics.cpu.usage.toFixed(1)}%`}
              description={`Load: ${dashboardData.system_metrics.cpu.load_avg_1.toFixed(2)}`}
              icon={IconActivity}
            />
            <StatsCard
              title="Memory Usage"
              value={`${dashboardData.system_metrics.memory.usage.toFixed(1)}%`}
              description={`${formatBytes(dashboardData.system_metrics.memory.used)} / ${formatBytes(dashboardData.system_metrics.memory.total)}`}
              icon={IconDeviceDesktop}
            />
            <StatsCard
              title="Disk Usage"
              value={`${dashboardData.system_metrics.disk.usage.toFixed(1)}%`}
              description={`${formatBytes(dashboardData.system_metrics.disk.used)} / ${formatBytes(dashboardData.system_metrics.disk.total)}`}
              icon={IconDeviceDesktop}
            />
            <StatsCard
              title="MQTT Memory"
              value={formatBytes(dashboardData.server_info.memory_alloc)}
              description="MQTT broker memory usage"
              icon={IconMessage}
            />
          </div>
        )}

        {/* System & MQTT Metrics */}
        <div>
          {isDashboardLoading ? (
            <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
              <Skeleton className="h-96" />
              <Skeleton className="h-96" />
            </div>
          ) : dashboardData ? (
            <SystemMetrics
              systemData={dashboardData.system_metrics}
              serverInfo={dashboardData.server_info}
            />
          ) : null}
        </div>

        {/* Connection Statistics */}
        {dashboardData && (
          <div className="grid gap-4 md:grid-cols-3">
            <StatsCard
              title="Bytes In"
              value={formatBytes(dashboardData.server_info.bytes_received)}
              description="Total bytes received"
            />
            <StatsCard
              title="Bytes Out"
              value={formatBytes(dashboardData.server_info.bytes_sent)}
              description="Total bytes sent"
            />
            <StatsCard
              title="Subscriptions"
              value={formatNumber(dashboardData.server_info.subscriptions)}
              description="Current subscriptions count"
            />
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
