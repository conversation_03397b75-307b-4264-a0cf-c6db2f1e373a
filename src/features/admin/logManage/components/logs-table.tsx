import { useMemo } from 'react'
import { DataTable, DataTableColumn } from '@/components/data-table'
import { LogEntry } from '../data/schema'
import { GetLogsParams, LogLevel, LogAction } from '@/api/logs'
import { logLevels, logActions } from '../data/data'
import { LogsCleanupPopover } from './logs-cleanup-popover'

interface LogsTableProps {
  columns: DataTableColumn<LogEntry>[]
  data: LogEntry[]
  pagination?: { page: number; pageSize: number; total: number }
  isLoading?: boolean
  error?: any
  onParamsChange?: (params: GetLogsParams) => void
  searchValue?: string
  selectedLevel?: LogLevel | '' | 'all'
  selectedAction?: LogAction | '' | 'all'
  startDate?: Date | null
  endDate?: Date | null
  onFilterChange?: (filters: {
    search?: string
    level?: LogLevel | '' | 'all'
    action?: LogAction | '' | 'all'
    startDate?: Date | null
    endDate?: Date | null
  }) => void
  onReset?: () => void
}

export function LogsTable({
  columns,
  data,
  pagination,
  isLoading = false,
  error,
  onParamsChange,
  searchValue = '',
  selectedLevel = 'all',
  selectedAction = 'all',
  startDate = null,
  endDate = null,
  onFilterChange,
  onReset
}: LogsTableProps) {
  // 筛选器配置
  const filterConfigs = useMemo(() => [
    {
      key: 'level',
      label: 'Level',
      type: 'select' as const,
      options: logLevels.map(({ value, label }) => ({ value, label })),
      allLabel: 'All Levels',
    },
    {
      key: 'action',
      label: 'Action',
      type: 'select' as const,
      options: logActions.map(({ value, label }) => ({ value, label })),
      allLabel: 'All Actions',
    },
  ], [])

  // 当前筛选器值
  const filterValues = useMemo(() => ({
    search: searchValue,
    level: selectedLevel,
    action: selectedAction,
    startDate,
    endDate,
  }), [searchValue, selectedLevel, selectedAction, startDate, endDate])

  // 构建当前筛选状态用于显示
  const currentServerFilters = useMemo(() => {
    const filters: Record<string, string[]> = {}
    if (selectedLevel && selectedLevel !== 'all') {
      filters.level = [selectedLevel]
    }
    if (selectedAction && selectedAction !== 'all') {
      filters.action = [selectedAction]
    }
    return filters
  }, [selectedLevel, selectedAction])

  return (
    <DataTable
      columns={columns}
      data={data}
      isLoading={isLoading}
      emptyMessage="No logs found."
      searchPlaceholder="Search logs..."
      searchFields={['message', 'user_id', 'ip']}
      filters={[
        {
          id: 'level',
          title: 'Level',
          options: [
            { label: 'Debug', value: 'debug' },
            { label: 'Info', value: 'info' },
            { label: 'Warning', value: 'warn' },
            { label: 'Error', value: 'error' },
          ]
        },
        {
          id: 'action',
          title: 'Action',
          options: [
            { label: 'Create', value: 'create' },
            { label: 'Update', value: 'update' },
            { label: 'Delete', value: 'delete' },
            { label: 'Login', value: 'login' },
          ]
        }
      ]}
      pageSize={pagination?.pageSize || 10}
      showPagination={true}
      showToolbar={true}
      toolbarActions={<LogsCleanupPopover />}
      serverSidePagination={true}
      // 服务端分页配置
      serverPaginationInfo={pagination ? {
        currentPage: pagination.page,
        totalPages: Math.ceil(pagination.total / pagination.pageSize),
        pageSize: pagination.pageSize,
        totalItems: pagination.total,
      } : undefined}
      onServerPageChange={(page) => onParamsChange?.({ page })}
      onServerPageSizeChange={(pageSize) => onParamsChange?.({ page: 1, limit: pageSize })}
      onServerSearchChange={(search) => onFilterChange?.({ search })}
      onServerFilterChange={(filterId, value) => {
        const filterUpdate: any = {}
        if (filterId === 'level') {
          filterUpdate.level = value === '' ? 'all' : value
        } else if (filterId === 'action') {
          filterUpdate.action = value === '' ? 'all' : value
        }
        onFilterChange?.(filterUpdate)
      }}
      // 传递当前筛选状态
      serverSearchValue={searchValue}
      serverSelectedFilters={currentServerFilters}
    />
  )
}
