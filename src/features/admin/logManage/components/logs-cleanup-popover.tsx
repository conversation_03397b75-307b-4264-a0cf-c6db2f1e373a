import { useState } from 'react'
import { useMutation, useQueryClient } from '@/hooks/use-api'
import { toast } from 'sonner'
import { Trash2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Settings, Calendar } from 'lucide-react'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { LogsAPI, CleanupLogsRequest } from '@/api/logs'

interface LogsCleanupPopoverProps {
  disabled?: boolean
}

export function LogsCleanupPopover({ disabled = false }: LogsCleanupPopoverProps) {
  const [open, setOpen] = useState(false)
  const [days, setDays] = useState<string>('30')
  const [confirmationStep, setConfirmationStep] = useState(false)

  const queryClient = useQueryClient()

  // 清理日志的 mutation
  const cleanupMutation = useMutation({
    mutationFn: (params: CleanupLogsRequest) => LogsAPI.cleanupLogs(params),
    onSuccess: (response) => {
      if (response.success && response.data) {
        toast.success(`Cleanup completed: ${response.data.count} logs deleted`)
        queryClient.invalidateQueries({ queryKey: ['logs'] })
        setOpen(false)
        handleReset()
      } else {
        toast.error(response.message || 'Cleanup failed')
      }
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Cleanup failed, please try again')
    },
  })

  // 重置表单
  const handleReset = () => {
    setDays('30')
    setConfirmationStep(false)
  }

  // 计算清理时间点
  const getCleanupDate = () => {
    const daysNum = parseInt(days) || 30
    const date = new Date()
    date.setDate(date.getDate() - daysNum)
    return date.toISOString().slice(0, 19).replace('T', ' ') // 格式：2006-01-02 15:04:05
  }

  // 处理清理确认
  const handleConfirm = () => {
    setConfirmationStep(true)
  }

  // 处理实际清理
  const handleCleanup = () => {
    const params: CleanupLogsRequest = {
      before: getCleanupDate(),
    }

    cleanupMutation.mutate(params)
  }

  // 处理 popover 关闭
  const handleOpenChange = (newOpen: boolean) => {
    if (!cleanupMutation.isPending) {
      setOpen(newOpen)
      if (!newOpen) {
        handleReset()
      }
    }
  }

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          disabled={disabled}
          className="h-8"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Cleanup Logs
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          {/* 标题 */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <h4 className="font-medium">Cleanup Settings</h4>
            </div>
            <p className="text-sm text-muted-foreground">
              Clean up old system logs to free storage space.
            </p>
          </div>

          <Separator />

          {!confirmationStep ? (
            <>
              {/* 保留天数 */}
              <div className="space-y-2">
                <Label htmlFor="cleanup-days" className="text-sm">Retention Days</Label>
                <Input
                  id="cleanup-days"
                  type="number"
                  min="1"
                  max="365"
                  value={days}
                  onChange={(e) => setDays(e.target.value)}
                  placeholder="30"
                  disabled={cleanupMutation.isPending}
                  className="h-8"
                />
                <p className="text-xs text-muted-foreground">
                  Delete logs older than {days || 30} days
                </p>
              </div>

              {/* 清理时间点预览 */}
              <div className="rounded-lg border bg-muted/50 p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Cleanup Date</span>
                </div>
                <p className="text-sm">
                  Will delete logs before: <span className="font-mono font-medium">{getCleanupDate()}</span>
                </p>
              </div>

              <Button
                size="sm"
                onClick={handleConfirm}
                disabled={cleanupMutation.isPending}
                className="w-full"
              >
                Continue
              </Button>
            </>
          ) : (
            <>
              {/* 确认步骤 */}
              <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-3">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-destructive" />
                  <span className="text-sm font-medium text-destructive">Warning</span>
                </div>
                <p className="text-sm text-destructive">
                  This action will permanently delete logs older than {days || 30} days.
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Cleanup date: {getCleanupDate()}
                </p>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setConfirmationStep(false)}
                  disabled={cleanupMutation.isPending}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={handleCleanup}
                  disabled={cleanupMutation.isPending}
                  className="flex-1"
                >
                  {cleanupMutation.isPending ? 'Cleaning...' : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Confirm Delete
                    </>
                  )}
                </Button>
              </div>
            </>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}
