import { createContext, useContext, ReactNode } from 'react'

interface LogsContextType {
  // 可以在这里添加日志相关的全局状态
  // 例如：选中的日志条目、统计数据等
}

const LogsContext = createContext<LogsContextType | undefined>(undefined)

export function useLogsContext() {
  const context = useContext(LogsContext)
  if (context === undefined) {
    throw new Error('useLogsContext must be used within a LogsProvider')
  }
  return context
}

interface LogsProviderProps {
  children: ReactNode
}

export default function LogsProvider({ children }: LogsProviderProps) {
  const value: LogsContextType = {
    // 初始化上下文值
  }

  return (
    <LogsContext.Provider value={value}>
      {children}
    </LogsContext.Provider>
  )
}
