import { useState, useCallback, useMemo } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { columns } from './components/logs-columns'
import { LogsTable } from './components/logs-table'
import LogsProvider from './context/logs-context'
import { useLogs } from './hooks/use-logs'
import { GetLogsParams, LogLevel, LogAction } from '@/api/logs'
import { format } from 'date-fns'

export default function Logs() {
  // 分页和过滤状态
  const [params, setParams] = useState<GetLogsParams>({
    page: 1,
    limit: 10,
  })

  // 筛选器UI状态 - 与API参数分离管理
  const [searchValue, setSearchValue] = useState('')
  const [selectedLevel, setSelectedLevel] = useState<LogLevel | '' | 'all'>('all')
  const [selectedAction, setSelectedAction] = useState<LogAction | '' | 'all'>('all')
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)

  // 获取日志数据
  const { data: logsResponse, isLoading, error } = useLogs(params)

  // 稳定的参数更新函数
  const handleParamsChange = useCallback((newParams: GetLogsParams) => {
    setParams((prevParams) => {
      // 创建一个新的参数对象，基于现有参数
      const updated: GetLogsParams = { ...prevParams }

      // 处理参数更新
      Object.entries(newParams).forEach(([key, value]) => {
        if (value !== undefined) {
          // 更新有值的字段
          updated[key as keyof GetLogsParams] = value as any
        } else {
          // 如果值为undefined，删除该字段
          delete updated[key as keyof GetLogsParams]
        }
      })

      return updated
    })
  }, [])

  // 处理筛选器状态更新
  const handleFilterChange = useCallback((filters: {
    search?: string
    level?: LogLevel | '' | 'all'
    action?: LogAction | '' | 'all'
    startDate?: Date | null
    endDate?: Date | null
  }) => {
    // 更新UI状态
    if (filters.search !== undefined) {
      setSearchValue(filters.search)
    }
    if (filters.level !== undefined) {
      setSelectedLevel(filters.level)
    }
    if (filters.action !== undefined) {
      setSelectedAction(filters.action)
    }
    if (filters.startDate !== undefined) {
      setStartDate(filters.startDate)
    }
    if (filters.endDate !== undefined) {
      setEndDate(filters.endDate)
    }

    // 获取当前参数状态，然后构建新的参数
    setParams(prevParams => {
      const newParams: GetLogsParams = {
        page: 1, // 重置到第一页
        limit: prevParams.limit || 10, // 保留当前的limit值
      }

      // 处理搜索值 - 只有当搜索值不为空时才添加search参数
      const searchTerm = filters.search !== undefined ? filters.search : searchValue
      if (searchTerm && searchTerm.trim()) {
        newParams.search = searchTerm.trim()
      }

      // 处理级别筛选 - 只有当不是 'all' 时才添加参数
      const levelValue = filters.level !== undefined ? filters.level : selectedLevel
      if (levelValue && levelValue !== 'all') {
        newParams.level = levelValue as LogLevel
      }

      // 处理操作筛选 - 只有当不是 'all' 时才添加参数
      const actionValue = filters.action !== undefined ? filters.action : selectedAction
      if (actionValue && actionValue !== 'all') {
        newParams.action = actionValue as LogAction
      }

      // 处理时间范围
      const currentStartDate = filters.startDate !== undefined ? filters.startDate : startDate
      const currentEndDate = filters.endDate !== undefined ? filters.endDate : endDate

      if (currentStartDate) {
        newParams.start_time = format(currentStartDate, 'yyyy-MM-dd HH:mm:ss')
      }

      if (currentEndDate) {
        newParams.end_time = format(currentEndDate, 'yyyy-MM-dd HH:mm:ss')
      }

      return newParams
    })
  }, [searchValue, selectedLevel, selectedAction, startDate, endDate, handleParamsChange])

  // 处理重置
  const handleReset = useCallback(() => {
    setSearchValue('')
    setSelectedLevel('all')
    setSelectedAction('all')
    setStartDate(null)
    setEndDate(null)
    
    setParams(prevParams => ({
      page: 1,
      limit: prevParams.limit || 10, // 保留当前的limit值
    }))
  }, [])

  // 使用 useMemo 缓存分页对象，避免重新渲染
  const paginationInfo = useMemo(() => {
    if (!logsResponse) return undefined
    return {
      page: params.page || 1,
      pageSize: params.limit || 10,
      total: logsResponse.total,
    }
  }, [logsResponse?.total, params.page, params.limit])

  return (
    <LogsProvider>
      <DashboardLayout
        headerFixed={true}
        pageHeader={{
          title: 'System Logs',
          description: 'View and manage system logs and activities.',
        }}
      >
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <LogsTable
            data={logsResponse?.list || []}
            columns={columns}
            pagination={paginationInfo}
            isLoading={isLoading}
            error={error}
            onParamsChange={handleParamsChange}
            searchValue={searchValue}
            selectedLevel={selectedLevel}
            selectedAction={selectedAction}
            startDate={startDate}
            endDate={endDate}
            onFilterChange={handleFilterChange}
            onReset={handleReset}
          />
        </div>
      </DashboardLayout>
    </LogsProvider>
  )
}
