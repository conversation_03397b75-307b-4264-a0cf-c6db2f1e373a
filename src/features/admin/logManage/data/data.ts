import {
  AlertCircle,
  AlertTriangle,
  Info,
  Bug,
  Shield,
  Wifi,
  Settings,
  Database,
  Plus,
  Edit,
  Trash2,
  LogIn,
} from 'lucide-react'
import { LogLevel, LogModule, LogAction } from './schema'

// 日志级别配置
export const logLevels = [
  {
    value: 'info' as LogLevel,
    label: 'Info',
    icon: Info,
  },
  {
    value: 'warn' as LogLevel,
    label: 'Warning',
    icon: AlertTriangle,
  },
  {
    value: 'error' as LogLevel,
    label: 'Error',
    icon: AlertCircle,
  },
  {
    value: 'debug' as LogLevel,
    label: 'Debug',
    icon: Bug,
  },
]

// 日志模块配置
export const logModules = [
  {
    value: 'auth' as LogModule,
    label: 'Authentication',
    icon: Shield,
  },
  {
    value: 'mqtt' as LogModule,
    label: 'MQTT',
    icon: Wifi,
  },
  {
    value: 'system' as LogModule,
    label: 'System',
    icon: Settings,
  },
  {
    value: 'config' as LogModule,
    label: 'Configuration',
    icon: Database,
  },
]

// 日志操作配置
export const logActions = [
  {
    value: 'create' as LogAction,
    label: 'Create',
    icon: Plus,
  },
  {
    value: 'update' as LogAction,
    label: 'Update',
    icon: Edit,
  },
  {
    value: 'delete' as LogAction,
    label: 'Delete',
    icon: Trash2,
  },
  {
    value: 'login' as LogAction,
    label: 'Login',
    icon: LogIn,
  },
]

// 日志级别样式映射
export const levelStyles = new Map<LogLevel, string>([
  ['info', 'text-blue-600 bg-blue-50 border-blue-200'],
  ['warn', 'text-yellow-600 bg-yellow-50 border-yellow-200'],
  ['error', 'text-red-600 bg-red-50 border-red-200'],
  ['debug', 'text-gray-600 bg-gray-50 border-gray-200'],
])

// 日志级别标签映射
export const levelLabels = new Map<LogLevel, string>([
  ['info', 'Info'],
  ['warn', 'Warning'],
  ['error', 'Error'],
  ['debug', 'Debug'],
])

// 模块标签映射
export const moduleLabels = new Map<LogModule, string>([
  ['auth', 'Auth'],
  ['mqtt', 'MQTT'],
  ['system', 'System'],
  ['config', 'Config'],
])

// 操作标签映射
export const actionLabels = new Map<LogAction, string>([
  ['create', 'Create'],
  ['update', 'Update'],
  ['delete', 'Delete'],
  ['login', 'Login'],
])
