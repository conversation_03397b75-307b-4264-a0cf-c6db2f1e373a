import { z } from 'zod'

export const logLevelSchema = z.union([
  z.literal('info'),
  z.literal('warn'),
  z.literal('error'),
  z.literal('debug'),
])
export type LogLevel = z.infer<typeof logLevelSchema>

export const logModuleSchema = z.union([
  z.literal('auth'),
  z.literal('mqtt'),
  z.literal('system'),
  z.literal('config'),
])
export type LogModule = z.infer<typeof logModuleSchema>

export const logActionSchema = z.union([
  z.literal('create'),
  z.literal('update'),
  z.literal('delete'),
  z.literal('login'),
])
export type LogAction = z.infer<typeof logActionSchema>

const logEntrySchema = z.object({
  id: z.string(),
  level: logLevelSchema,
  module: logModuleSchema,
  action: logActionSchema,
  message: z.string(),
  user_id: z.string().optional(),
  user_name: z.string().optional(),
  ip: z.string().optional(),
  user_agent: z.string().optional(),
  details: z.record(z.any()).optional(),
  timestamp: z.string(),
})
export type LogEntry = z.infer<typeof logEntrySchema>

export const logListSchema = z.array(logEntrySchema)
