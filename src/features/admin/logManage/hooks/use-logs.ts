import { useQuery, useMutation, useQueryClient } from '@/hooks/use-api'
import { LogsAPI, GetLogsParams, CleanupLogsRequest } from '@/api/logs'

// 获取日志列表
export function useLogs(params?: GetLogsParams) {
  return useQuery({
    queryKey: ['logs', params],
    queryFn: () => LogsAPI.getLogs(params),
    select: (data) => data.data,
    // staleTime: 30000, // 30秒内不重新获取 - 不支持缓存
  })
}

// 清理日志
export function useCleanupLogs() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CleanupLogsRequest) => LogsAPI.cleanupLogs(data),
    onSuccess: () => {
      // 清理成功后刷新日志列表和统计数据
      queryClient.invalidateQueries({ queryKey: ['logs'] })
    },
  })
}
