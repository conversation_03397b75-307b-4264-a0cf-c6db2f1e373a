interface Props {
  children: React.ReactNode
}

export default function AuthLayout({ children }: Props) {
  return (
    <div className='min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900'>
      {/* 装饰性背景元素 */}
      <div className='absolute inset-0 overflow-hidden'>
        <div className='absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-full blur-3xl'></div>
        <div className='absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-pink-600/20 rounded-full blur-3xl'></div>
        <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-3xl'></div>
      </div>

      {/* 主要内容区域 */}
      <div className='relative flex min-h-screen items-center justify-center p-4'>
        <div className='w-full max-w-md space-y-8'>
          {/* Logo 区域 */}
          <div className='text-center'>
            <div className='mx-auto mb-6 w-64 flexitems-center justify-center rounded-2xl backdrop-blur-sm'>
              <img
                src='/logo.png'
                alt='Beacon Cloud Platform'
                className='object-contain'
              />
            </div>
            <p className='mt-2 text-sm text-slate-600 dark:text-slate-400'>
              Welcome back! Please sign in to your account
            </p>
          </div>

          {/* 认证表单容器 */}
          <div className='relative'>
            {/* 背景装饰 */}
            <div className='absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5 rounded-2xl blur-xl'></div>

            {/* 主要卡片 */}
            <div className='relative bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl ring-1 ring-black/5 dark:bg-slate-800/80 dark:ring-white/10'>
              {children}
            </div>
          </div>

          {/* 底部信息 */}
          <div className='text-center'>
            <p className='text-xs text-slate-500 dark:text-slate-400'>
              © 2024 Beacon Cloud Platform. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
