import { HTMLAttributes, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import { CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp'
import { AuthAPI } from '@/api/auth'

type ForgotPasswordFormProps = HTMLAttributes<HTMLFormElement>

// 第一步：输入邮箱
const emailFormSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
})

// 第二步：输入验证码
const codeFormSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6, '验证码必须是6位数字').max(6, '验证码必须是6位数字'),
})

type EmailFormValues = z.infer<typeof emailFormSchema>
type CodeFormValues = z.infer<typeof codeFormSchema>

export function ForgotPasswordForm({ className, ...props }: ForgotPasswordFormProps) {
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false)
  const [step, setStep] = useState<'email' | 'code'>('email')
  const [email, setEmail] = useState('')

  const emailForm = useForm<EmailFormValues>({
    resolver: zodResolver(emailFormSchema),
    defaultValues: {
      email: '',
    },
  })

  const codeForm = useForm<CodeFormValues>({
    resolver: zodResolver(codeFormSchema),
    defaultValues: {
      email: '',
      code: '',
    },
  })

  // 发送验证码
  async function onSendCode(data: EmailFormValues) {
    setIsLoading(true)

    try {
      const response = await AuthAPI.forgotPassword({
        email: data.email,
        send_code: true,
      })

      if (response.success) {
        setEmail(data.email)
        codeForm.setValue('email', data.email)
        setStep('code')
        toast.success('Verification code sent to your email!')
      } else {
        toast.error(response.message || 'Failed to send verification code')
      }
    } catch (error: any) {
      console.error('Send code error:', error)
      toast.error(error.response?.data?.message || 'Failed to send verification code, please check your email address')
    } finally {
      setIsLoading(false)
    }
  }

  // 验证验证码并重置密码
  async function onVerifyCode(data: CodeFormValues) {
    setIsLoading(true)

    try {
      const response = await AuthAPI.forgotPassword({
        email: data.email,
        send_code: false,
        code: data.code,
      })

      if (response.success) {
        toast.success('Password reset successful! New password has been sent to your email. Redirecting to login page...')
        // 延迟导航，让用户看到成功消息
        setTimeout(() => {
          navigate('/sign-in')
        }, 2000)
      } else {
        toast.error(response.message || 'Invalid verification code')
      }
    } catch (error: any) {
      console.error('Verify code error:', error)
      toast.error(error.response?.data?.message || 'Invalid verification code, please try again')
    } finally {
      setIsLoading(false)
    }
  }

  if (step === 'email') {
    return (
      <Form {...emailForm}>
        <form
          onSubmit={emailForm.handleSubmit(onSendCode)}
          className={cn('space-y-6', className)}
          {...props}
        >
          <FormField
            control={emailForm.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                  Email Address
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder='Enter your email address'
                    className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            className='w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium'
            disabled={isLoading}
          >
            {isLoading ? (
              <div className='flex items-center space-x-2'>
                <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
                <span>Sending...</span>
              </div>
            ) : (
              'Send Verification Code'
            )}
          </Button>
        </form>
      </Form>
    )
  }

  return (
    <div className="space-y-6">
      {/* 顶部状态指示区域 */}
      <div className="text-center space-y-3">
        <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
          <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Check your email</h3>
          <p className="text-sm text-slate-600 dark:text-slate-400 max-w-sm mx-auto">
            We've sent a 6-digit verification code to
          </p>
          <p className="text-sm font-medium text-slate-900 dark:text-white">{email}</p>
        </div>
      </div>

      {/* 表单区域 */}
      <Form {...codeForm}>
        <form
          onSubmit={codeForm.handleSubmit(onVerifyCode)}
          className={cn('space-y-6', className)}
          {...props}
        >
          {/* OTP输入区域 */}
          <FormField
            control={codeForm.control}
            name='code'
            render={({ field }) => (
              <FormItem className="space-y-4">
                <FormLabel className="text-center block text-sm font-medium text-slate-700 dark:text-slate-300">
                  Enter verification code
                </FormLabel>
                <FormControl>
                  <div className="flex justify-center">
                    <InputOTP maxLength={6} {...field}>
                      <InputOTPGroup className="gap-2">
                        <InputOTPSlot
                          index={0}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={1}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={2}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={3}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={4}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={5}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </FormControl>
                <FormMessage className="text-center" />
              </FormItem>
            )}
          />

          {/* 操作按钮区域 */}
          <div className='space-y-4'>
            <Button
              type="submit"
              className='w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium'
              disabled={isLoading}
            >
              {isLoading ? (
                <div className='flex items-center space-x-2'>
                  <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
                  <span>Verifying...</span>
                </div>
              ) : (
                'Reset Password'
              )}
            </Button>

            <div className="flex items-center justify-center space-x-4 text-sm">
              <Button
                type='button'
                variant='ghost'
                onClick={() => setStep('email')}
                disabled={isLoading}
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100 font-medium"
              >
                ← Back to email
              </Button>

              <span className="text-slate-300 dark:text-slate-600">|</span>

              <Button
                type='button'
                variant='ghost'
                onClick={() => {
                  // 重新发送验证码
                  const emailValue = codeForm.getValues('email')
                  if (emailValue) {
                    onSendCode({ email: emailValue })
                  }
                }}
                disabled={isLoading}
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100 font-medium"
              >
                Resend code
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {/* 底部提示信息 */}
      <div className="text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">
          Didn't receive the code? Check your spam folder or try resending.
        </p>
      </div>
    </div>
  )
}
