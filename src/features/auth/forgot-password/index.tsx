import { Link } from 'react-router-dom'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { ForgotPasswordForm } from './components/forgot-password-form'

export default function ForgotPassword() {
  return (
    <AuthLayout>
      <Card className='border-0 shadow-none bg-transparent'>
        <CardHeader className='space-y-1 pb-6'>
          <CardTitle className='text-2xl font-semibold tracking-tight text-center text-slate-900 dark:text-white'>
            Reset Password
          </CardTitle>
          <CardDescription className='text-center text-slate-600 dark:text-slate-400'>
            Enter your email address and we'll send you a verification code to reset your password
          </CardDescription>
        </CardHeader>
        <CardContent className='px-6 pb-6'>
          <ForgotPasswordForm />
        </CardContent>
        <CardFooter className='px-6 pb-6'>
          <div className='w-full text-center'>
            <p className='text-sm text-slate-600 dark:text-slate-400'>
              Remember your password?{' '}
              <Link
                to='/sign-in'
                className='font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300'
              >
                Back to sign in
              </Link>
            </p>
          </div>
        </CardFooter>
      </Card>
    </AuthLayout>
  )
}
