import { HTMLAttributes, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/custom_components/password-input'
import { AuthAPI } from '@/api/auth'
import { useAuthStore } from '@/stores/authStore'

type UserAuthFormProps = HTMLAttributes<HTMLFormElement>

const formSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Please enter your email' })
    .email({ message: 'Invalid email address' }),
  password: z
    .string()
    .min(1, { message: 'Please enter your password' })
    .min(8, { message: 'Password must be at least 8 characters long' }),
})

export function UserAuthForm({ className, ...props }: UserAuthFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { login } = useAuthStore()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  async function onSubmit(data: z.infer<typeof formSchema>) {
    setIsLoading(true)

    try {
      const response = await AuthAPI.login(data)

      if (response.success && response.data) {
        const { user, access_token, refresh_token } = response.data

        // 保存用户信息和token
        login(user, access_token, refresh_token)

        toast.success('登录成功！')

        // 跳转到原来要访问的页面或首页
        const redirectTo = searchParams.get('redirect') || '/dashboard'
        navigate(redirectTo)
      } else {
        toast.error(response.message || '登录失败')
      }
    } catch (error: any) {
      console.error('Login error:', error)
      toast.error(error.response?.data?.message || '登录失败，请检查邮箱和密码')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('space-y-6', className)}
        {...props}
      >
        <div className='space-y-4'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                  Email Address
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder='Enter your email address'
                    className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center justify-between'>
                  <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    Password
                  </FormLabel>
                  <Link
                    to='/forgot-password'
                    className='text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium'
                  >
                    Forgot password?
                  </Link>
                </div>
                <FormControl>
                  <PasswordInput
                    placeholder='Enter your password'
                    className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button
          className='w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium'
          disabled={isLoading}
        >
          {isLoading ? (
            <div className='flex items-center space-x-2'>
              <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
              <span>Signing in...</span>
            </div>
          ) : (
            'Sign In'
          )}
        </Button>

        <div className='text-center'>
          <p className='text-sm text-slate-600 dark:text-slate-400'>
            Don't have an account?{' '}
            <Link
              to='/sign-up'
              className='font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300'
            >
              Sign up for free
            </Link>
          </p>
        </div>
      </form>
    </Form>
  )
}
