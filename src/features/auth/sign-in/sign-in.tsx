import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { UserAuthForm } from './components/user-auth-form'

export default function SignIn2() {
  return (
    <AuthLayout>
      <Card className='border-0 shadow-none bg-transparent'>
        <CardHeader className='space-y-1 pb-6'>
          <CardTitle className='text-2xl font-semibold tracking-tight text-center text-slate-900 dark:text-white'>
            Sign In
          </CardTitle>
          <CardDescription className='text-center text-slate-600 dark:text-slate-400'>
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent className='px-6 pb-6'>
          <UserAuthForm />
        </CardContent>
      </Card>
    </AuthLayout>
  )
}
