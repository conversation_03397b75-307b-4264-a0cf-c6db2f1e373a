import { Link } from 'react-router-dom'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { SignUpForm } from './components/sign-up-form'

export default function SignUp() {
  return (
    <AuthLayout>
      <Card className='border-0 shadow-none bg-transparent'>
        <CardHeader className='space-y-1 pb-6'>
          <CardTitle className='text-2xl font-semibold tracking-tight text-center text-slate-900 dark:text-white'>
            Create Account
          </CardTitle>
          <CardDescription className='text-center text-slate-600 dark:text-slate-400'>
            Join us today and start your journey
          </CardDescription>
        </CardHeader>
        <CardContent className='px-6 pb-6'>
          <SignUpForm />
        </CardContent>
      </Card>
    </AuthLayout>
  )
}
