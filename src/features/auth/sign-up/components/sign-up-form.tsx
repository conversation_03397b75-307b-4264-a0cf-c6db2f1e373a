import { HTMLAttributes, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import { CheckCircle, XCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/custom_components/password-input'
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp'
import { AuthAPI } from '@/api/auth'

type SignUpFormProps = HTMLAttributes<HTMLFormElement>

const formSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Please enter your email' })
    .email({ message: 'Invalid email address' }),
  password: z
    .string()
    .min(1, { message: 'Please enter your password' })
    .min(8, { message: 'Password must be at least 8 characters long' }),
  confirmPassword: z
    .string()
    .min(1, { message: 'Please confirm your password' }),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  company: z.string().optional(),
  department: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

const otpSchema = z.object({
  code: z.string().min(6, { message: 'Please enter the 6-digit verification code' }),
})

// 注册流程状态
type RegistrationStep = 'form' | 'sending' | 'verification' | 'success'

export function SignUpForm({ className, ...props }: SignUpFormProps) {
  const navigate = useNavigate()
  const [step, setStep] = useState<RegistrationStep>('form')
  const [isLoading, setIsLoading] = useState(false)
  const [userEmail, setUserEmail] = useState('')
  const [sendError, setSendError] = useState('')

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      company: '',
      department: '',
    },
  })

  const otpForm = useForm<z.infer<typeof otpSchema>>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      code: '',
    },
  })

  async function sendVerificationCode() {
    const { email, password, firstName, lastName, company, department } = form.getValues()

    if (!email || !password) {
      toast.error('Please fill in email and password first')
      return
    }

    setStep('sending')
    setIsLoading(true)
    setSendError('')

    try {
      const response = await AuthAPI.registerSendCode({
        email,
        password,
        profile: {
          first_name: firstName,
          last_name: lastName,
          company,
          department,
        },
      })

      if (response.success) {
        setUserEmail(email)
        setStep('verification')
        toast.success('Verification code sent to your email!')
      } else {
        setSendError(response.message || 'Failed to send verification code')
        setStep('form')
        toast.error(response.message || 'Failed to send verification code')
      }
    } catch (error: any) {
      console.error('Send code error:', error)
      const errorMessage = error.response?.data?.error || 'Failed to send verification code'
      setSendError(errorMessage)
      setStep('form')
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  async function onSubmit(_data: z.infer<typeof formSchema>) {
    // 这个函数现在只处理发送验证码
    await sendVerificationCode()
  }

  async function onOtpSubmit(data: z.infer<typeof otpSchema>) {
    if (!userEmail) {
      toast.error('Please send verification code first')
      return
    }

    setIsLoading(true)

    try {
      const response = await AuthAPI.registerVerify({
        email: userEmail,
        code: data.code,
      })

      if (response.success) {
        setStep('success')
        toast.success('Registration successful! Redirecting to login page...')
        // 延迟导航，让用户看到成功消息
        setTimeout(() => {
          navigate('/sign-in')
        }, 2000)
      } else {
        toast.error(response.message || 'Registration failed')
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      toast.error(error.response?.data?.message || 'Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="flex flex-col items-center justify-center py-12 space-y-6">
      <div className="relative">
        <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-blue-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
      </div>
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Sending verification code</h3>
        <p className="text-sm text-slate-600 dark:text-slate-400 max-w-sm">
          Please wait while we send the verification code to your email address.
        </p>
      </div>
    </div>
  )

  // 渲染验证码输入状态
  const renderVerificationState = () => (
    <div className="space-y-6">
      <div className="text-center space-y-3">
        <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
          <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Check your email</h3>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          We've sent a 6-digit verification code to{' '}
          <span className="font-medium text-slate-900 dark:text-white">{userEmail}</span>
        </p>
      </div>

      <Form {...otpForm}>
        <form onSubmit={otpForm.handleSubmit(onOtpSubmit)} className="space-y-6">
          <FormField
            control={otpForm.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-center block text-sm font-medium text-slate-700 dark:text-slate-300">
                  Enter verification code
                </FormLabel>
                <FormControl>
                  <div className="flex justify-center">
                    <InputOTP maxLength={6} {...field}>
                      <InputOTPGroup className="gap-2">
                        <InputOTPSlot
                          index={0}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={1}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={2}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={3}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={4}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                        <InputOTPSlot
                          index={5}
                          className="w-12 h-12 text-lg font-semibold border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:focus:border-blue-400"
                        />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </FormControl>
                <FormMessage className="text-center" />
              </FormItem>
            )}
          />

          <div className="space-y-3">
            <Button
              type="submit"
              className="w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className='flex items-center space-x-2'>
                  <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
                  <span>Verifying...</span>
                </div>
              ) : (
                'Verify & Create Account'
              )}
            </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full h-11 bg-white/50 border-slate-200 hover:bg-slate-50 dark:bg-slate-700/50 dark:border-slate-600 dark:hover:bg-slate-600/50"
              onClick={() => setStep('form')}
              disabled={isLoading}
            >
              Back to form
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )

  // 渲染注册表单
  const renderFormState = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('space-y-6', className)}
        {...props}
      >
        <div className='space-y-4'>
          <div className='grid grid-cols-2 gap-3'>
            <FormField
              control={form.control}
              name='firstName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    First Name
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='First Name'
                      className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='lastName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    Last Name
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Last Name'
                      className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                  Email Address <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder='<EMAIL>'
                    className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className='grid grid-cols-2 gap-3'>
            <FormField
              control={form.control}
              name='company'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    Company
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Your company'
                      className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='department'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                    Department
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Your department'
                      className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                  Password <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder='Enter your password'
                    className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='confirmPassword'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-slate-700 dark:text-slate-300'>
                  Confirm Password <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder='Confirm your password'
                    className='h-11 bg-white/50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 dark:bg-slate-700/50 dark:border-slate-600 dark:focus:border-blue-400'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button
          type='submit'
          disabled={isLoading}
          className='w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium'
        >
          {isLoading ? (
            <div className='flex items-center space-x-2'>
              <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
              <span>Sending Code...</span>
            </div>
          ) : (
            'Send Verification Code'
          )}
        </Button>

        <div className='text-center'>
          <p className='text-sm text-slate-600 dark:text-slate-400'>
            Already have an account?{' '}
            <Link
              to='/sign-in'
              className='font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300'
            >
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </Form>
  )

  // 主要的返回逻辑
  return (
    <div className={cn('w-full', className)}>
      {sendError && (
        <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md flex items-center">
          <XCircle className="h-4 w-4 mr-2" />
          {sendError}
        </div>
      )}

      {step === 'form' && renderFormState()}
      {step === 'sending' && renderLoadingState()}
      {step === 'verification' && renderVerificationState()}
    </div>
  )
}
