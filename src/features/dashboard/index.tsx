import { useTranslation } from 'react-i18next'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { Search } from '@/components/custom_components/search'
import { ThemeSwitch } from '@/components/custom_components/theme-switch'
import { LanguageSwitch } from '@/components/custom_components/language-switch'
import { UserDropdown } from '@/components/custom_components/user-dropdown'
import { headerUserMenuItems } from '@/components/custom_components/user-dropdown-config'
import { sidebarDataConfig } from '@/components/layout/data/sidebar-data'

export default function Dashboard() {
  const { t } = useTranslation()

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <LanguageSwitch />
          <UserDropdown
            user={sidebarDataConfig.user}
            variant="header"
            menuItems={headerUserMenuItems}
          />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='flex items-center justify-center min-h-[60vh]'>
          <div className='text-center space-y-4'>
            <h1 className='text-3xl font-bold tracking-tight text-slate-900 dark:text-white'>
              {t('dashboard.title')}
            </h1>
            <p className='text-lg text-slate-600 dark:text-slate-400 max-w-md'>
              Welcome to your dashboard. This area is ready for development.
            </p>
            <div className='mt-8 p-6 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700'>
              <p className='text-sm text-slate-500 dark:text-slate-400'>
                Dashboard content will be implemented here
              </p>
            </div>
          </div>
        </div>
      </Main>
    </>
  )
}


