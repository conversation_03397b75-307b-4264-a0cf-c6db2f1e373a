import Cookies from 'js-cookie'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthAPI } from '@/api/auth'

const ACCESS_TOKEN = 'beacon_access_token'
const REFRESH_TOKEN = 'beacon_refresh_token'

// 用户资料接口
interface UserProfile {
  first_name?: string
  last_name?: string
  display_name?: string
  avatar?: string
  company?: string
  department?: string
}

// 用户信息接口 - 根据后端API响应格式
interface AuthUser {
  id: string
  email: string
  role: string
  status: string
  profile?: UserProfile
  created_at: string
  updated_at: string
  last_login_at?: string
}

// 认证状态接口
interface AuthState {
  user: AuthUser | null
  accessToken: string
  refreshToken: string
  isAuthenticated: boolean
  isLoading: boolean

  // 用户相关方法
  setUser: (user: AuthUser | null) => void

  // Token相关方法
  setTokens: (accessToken: string, refreshToken: string) => void
  setAccessToken: (accessToken: string) => void
  setRefreshToken: (refreshToken: string) => void
  clearTokens: () => void

  // 认证状态方法
  setLoading: (loading: boolean) => void
  login: (user: AuthUser, accessToken: string, refreshToken: string) => void
  logout: () => void

  // 初始化方法
  initialize: () => Promise<void>

  // 获取用户资料
  fetchUserProfile: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => {
      // 从cookie中获取初始token
      const initAccessToken = Cookies.get(ACCESS_TOKEN) || ''
      const initRefreshToken = Cookies.get(REFRESH_TOKEN) || ''

      return {
        // 初始状态
        user: null,
        accessToken: initAccessToken,
        refreshToken: initRefreshToken,
        isAuthenticated: !!initAccessToken,
        isLoading: false,

        // 用户相关方法
        setUser: (user: AuthUser | null) =>
          set({ user }),

        // Token相关方法
        setTokens: (accessToken: string, refreshToken: string) => {
          Cookies.set(ACCESS_TOKEN, accessToken, { expires: 1 }) // 1天过期
          Cookies.set(REFRESH_TOKEN, refreshToken, { expires: 7 }) // 7天过期
          set({
            accessToken,
            refreshToken,
            isAuthenticated: true
          })
        },

        setAccessToken: (accessToken: string) => {
          Cookies.set(ACCESS_TOKEN, accessToken, { expires: 1 })
          set({ accessToken, isAuthenticated: !!accessToken })
        },

        setRefreshToken: (refreshToken: string) => {
          Cookies.set(REFRESH_TOKEN, refreshToken, { expires: 7 })
          set({ refreshToken })
        },

        clearTokens: () => {
          Cookies.remove(ACCESS_TOKEN)
          Cookies.remove(REFRESH_TOKEN)
          set({
            accessToken: '',
            refreshToken: '',
            isAuthenticated: false
          })
        },

        // 认证状态方法
        setLoading: (loading: boolean) =>
          set({ isLoading: loading }),

        login: (user: AuthUser, accessToken: string, refreshToken: string) => {
          Cookies.set(ACCESS_TOKEN, accessToken, { expires: 1 })
          Cookies.set(REFRESH_TOKEN, refreshToken, { expires: 7 })
          set({
            user,
            accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false
          })
        },

        logout: () => {
          Cookies.remove(ACCESS_TOKEN)
          Cookies.remove(REFRESH_TOKEN)
          set({
            user: null,
            accessToken: '',
            refreshToken: '',
            isAuthenticated: false,
            isLoading: false
          })
        },

        // 获取用户资料
        fetchUserProfile: async () => {
          const { accessToken } = get()
          if (!accessToken) return

          try {
            set({ isLoading: true })
            const response = await AuthAPI.getProfile()
            if (response.success && response.data) {
              set({ user: response.data })
            }
          } catch (error) {
            console.error('Failed to fetch user profile:', error)
          } finally {
            set({ isLoading: false })
          }
        },

        // 初始化方法 - 检查token有效性并恢复用户信息
        initialize: async () => {
          const accessToken = Cookies.get(ACCESS_TOKEN)
          const refreshToken = Cookies.get(REFRESH_TOKEN)

          if (accessToken && refreshToken) {
            set({
              accessToken,
              refreshToken,
              isAuthenticated: true
            })

            // 如果没有用户信息，尝试获取
            const { user } = get()
            if (!user) {
              await get().fetchUserProfile()
            }
          } else {
            set({
              user: null,
              accessToken: '',
              refreshToken: '',
              isAuthenticated: false
            })
          }
        }
      }
    },
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user, // 只持久化用户信息
      }),
    }
  )
)

// 导出便捷的hook
export const useAuth = () => useAuthStore()

// 导出类型
export type { AuthUser, UserProfile, AuthState }
