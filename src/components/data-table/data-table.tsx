import * as React from 'react'
import { DataTableCore } from './data-table-core'
import { DataTableToolbar } from './data-table-toolbar'
import { DataTablePagination } from './data-table-pagination'
import { useDataTable } from './use-data-table'
import { DataTableProps } from './types'

// 辅助函数：获取嵌套属性值
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

export function DataTable<TData>({
  columns,
  data,
  emptyMessage = 'No results.',
  className,
  isLoading = false,
  serverSidePagination = false,
  searchPlaceholder = 'Search...',
  searchFields = [],
  filters = [],
  pageSize = 10,
  showPagination = true,
  showToolbar = true,
  toolbarActions,
  // 服务端分页相关
  serverPaginationInfo,
  onServerPageChange,
  onServerPageSizeChange,
  onServerSearchChange,
  onServerFilterChange,
  // 服务端筛选状态
  serverSearchValue = '',
  serverSelectedFilters = {},
}: DataTableProps<TData>) {
  // 根据是否为服务端分页选择不同的逻辑
  const clientSideTable = useDataTable({
    data,
    initialPageSize: pageSize,
    serverSide: false,
    // 添加自定义筛选函数来支持搜索字段
    filterFn: (item, filters, search) => {
      // 搜索筛选
      if (search && searchFields.length > 0) {
        const searchLower = search.toLowerCase()
        const matchesSearch = searchFields.some(field => {
          const value = getNestedValue(item, field as string)
          return String(value).toLowerCase().includes(searchLower)
        })
        if (!matchesSearch) return false
      }

      // 其他筛选
      for (const [key, value] of Object.entries(filters)) {
        if (value && value !== 'all') {
          const itemValue = getNestedValue(item, key)
          if (String(itemValue) !== value) {
            return false
          }
        }
      }

      return true
    }
  })

  // 根据分页模式选择数据和处理函数
  const tableData = serverSidePagination ? data : clientSideTable.data
  const currentSort = serverSidePagination ? { column: '', direction: null } : clientSideTable.sort
  const paginationInfo = serverSidePagination ? serverPaginationInfo : clientSideTable.paginationInfo

  // 处理函数
  const handlePageChange = serverSidePagination
    ? (page: number) => onServerPageChange?.(page)
    : clientSideTable.goToPage

  const handlePageSizeChange = serverSidePagination
    ? (pageSize: number) => onServerPageSizeChange?.(pageSize)
    : clientSideTable.changePageSize

  const handleSearchChange = serverSidePagination
    ? (search: string) => onServerSearchChange?.(search)
    : clientSideTable.updateSearch

  const handleSortChange = serverSidePagination
    ? () => {} // 服务端排序暂不实现
    : (column: string) => {
        const newDirection =
          currentSort.column === column && currentSort.direction === 'asc'
            ? 'desc'
            : currentSort.column === column && currentSort.direction === 'desc'
            ? null
            : 'asc'
        clientSideTable.updateSort({ column, direction: newDirection })
      }

  // 处理筛选器变化
  const handleFilterChange = (filterId: string, values: string[]) => {
    const value = values[0] || ''
    if (serverSidePagination && onServerFilterChange) {
      onServerFilterChange(filterId, value)
    } else {
      clientSideTable.updateFilter(filterId, value)
    }
  }

  const handleClearFilters = () => {
    if (serverSidePagination) {
      // 服务端模式：触发所有筛选器重置
      filters.forEach(filter => {
        onServerFilterChange?.(filter.id, '')
      })
      onServerSearchChange?.('')
    } else {
      clientSideTable.resetFilters()
    }
  }

  // 获取当前筛选状态
  const currentFilters = serverSidePagination
    ? Object.fromEntries(
        Object.entries(serverSelectedFilters).map(([key, values]) => [
          key,
          values[0] || ''
        ])
      )
    : clientSideTable.filters

  const currentSearch = serverSidePagination
    ? serverSearchValue
    : clientSideTable.search

  return (
    <div className={`space-y-4 ${className || ''}`}>
      {showToolbar && (
        <DataTableToolbar
          searchValue={currentSearch}
          onSearchChange={handleSearchChange || (() => {})}
          searchPlaceholder={searchPlaceholder}
          filters={filters}
          selectedFilters={serverSidePagination
            ? serverSelectedFilters
            : Object.fromEntries(
                Object.entries(currentFilters).map(([key, value]) => [
                  key,
                  value ? [value] : []
                ])
              )
          }
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        >
          {toolbarActions}
        </DataTableToolbar>
      )}

      <DataTableCore
        columns={columns}
        data={tableData}
        isLoading={isLoading}
        emptyMessage={emptyMessage}
        sortable={!serverSidePagination}
        onSort={handleSortChange}
        currentSort={currentSort}
        className="rounded-md border"
      />

      {showPagination && paginationInfo && paginationInfo.totalItems > 0 && (
        <DataTablePagination
          paginationInfo={paginationInfo}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </div>
  )
}
