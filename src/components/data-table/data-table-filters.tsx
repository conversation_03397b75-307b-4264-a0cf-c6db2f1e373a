import { Check, PlusCircle, X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { FilterConfig } from './types'

interface DataTableFiltersProps {
  filters: FilterConfig[]
  selectedFilters: Record<string, string[]>
  onFilterChange: (filterId: string, values: string[]) => void
  onClearFilters: () => void
}

export function DataTableFilters({
  filters,
  selectedFilters,
  onFilterChange,
  onClearFilters,
}: DataTableFiltersProps) {
  const hasActiveFilters = Object.values(selectedFilters).some(values => values.length > 0)

  return (
    <div className="flex items-center space-x-2">
      {filters.map((filter) => {
        const selectedValues = selectedFilters[filter.id] || []
        
        return (
          <Popover key={filter.id}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 border-dashed">
                <PlusCircle className="mr-2 h-4 w-4" />
                {filter.title}
                {selectedValues.length > 0 && (
                  <>
                    <Separator orientation="vertical" className="mx-2 h-4" />
                    <Badge
                      variant="secondary"
                      className="rounded-sm px-1 font-normal lg:hidden"
                    >
                      {selectedValues.length}
                    </Badge>
                    <div className="hidden space-x-1 lg:flex">
                      {selectedValues.length > 2 ? (
                        <Badge
                          variant="secondary"
                          className="rounded-sm px-1 font-normal"
                        >
                          {selectedValues.length} selected
                        </Badge>
                      ) : (
                        filter.options
                          .filter((option) => selectedValues.includes(option.value))
                          .map((option) => (
                            <Badge
                              variant="secondary"
                              key={option.value}
                              className="rounded-sm px-1 font-normal"
                            >
                              {option.label}
                            </Badge>
                          ))
                      )}
                    </div>
                  </>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
              <Command>
                <CommandInput placeholder={filter.title} />
                <CommandList>
                  <CommandEmpty>No results found.</CommandEmpty>
                  <CommandGroup>
                    {filter.options.map((option) => {
                      const isSelected = selectedValues.includes(option.value)
                      return (
                        <CommandItem
                          key={option.value}
                          onSelect={() => {
                            if (isSelected) {
                              onFilterChange(
                                filter.id,
                                selectedValues.filter((value) => value !== option.value)
                              )
                            } else {
                              onFilterChange(filter.id, [...selectedValues, option.value])
                            }
                          }}
                        >
                          <div
                            className={cn(
                              "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                              isSelected
                                ? "bg-primary text-primary-foreground"
                                : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <Check className={cn("h-4 w-4")} />
                          </div>
                          <span>{option.label}</span>
                        </CommandItem>
                      )
                    })}
                  </CommandGroup>
                  {selectedValues.length > 0 && (
                    <>
                      <CommandSeparator />
                      <CommandGroup>
                        <CommandItem
                          onSelect={() => onFilterChange(filter.id, [])}
                          className="justify-center text-center"
                        >
                          Clear filter
                        </CommandItem>
                      </CommandGroup>
                    </>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        )
      })}
      
      {hasActiveFilters && (
        <Button
          variant="ghost"
          onClick={onClearFilters}
          className="h-8 px-2 lg:px-3"
        >
          Reset
          <X className="ml-2 h-4 w-4" />
        </Button>
      )}
    </div>
  )
}
