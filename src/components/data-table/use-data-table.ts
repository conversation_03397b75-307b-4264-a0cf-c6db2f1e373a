import { useState, useMemo, useCallback } from 'react'
import { 
  SortState, 
  PaginationState, 
  DataTableState, 
  UseDataTableOptions,
  PaginationInfo 
} from './types'

// 默认筛选函数
function defaultFilterFn<TData>(
  item: TData, 
  filters: Record<string, string>, 
  search: string
): boolean {
  // 搜索筛选
  if (search) {
    const searchLower = search.toLowerCase()
    const itemStr = JSON.stringify(item).toLowerCase()
    if (!itemStr.includes(searchLower)) {
      return false
    }
  }

  // 其他筛选
  for (const [key, value] of Object.entries(filters)) {
    if (value && value !== 'all') {
      const itemValue = (item as any)[key]
      if (itemValue !== value) {
        return false
      }
    }
  }

  return true
}

// 默认排序函数
function defaultSortFn<TData>(a: TData, b: TData, sort: SortState): number {
  if (!sort.direction || !sort.column) return 0

  const aValue = (a as any)[sort.column]
  const bValue = (b as any)[sort.column]

  let result = 0
  
  if (aValue < bValue) result = -1
  else if (aValue > bValue) result = 1
  
  return sort.direction === 'desc' ? -result : result
}

export function useDataTable<TData>({
  data,
  initialPageSize = 10,
  initialSort = { column: '', direction: null },
  initialFilters = {},
  serverSide = false,
  filterFn = defaultFilterFn,
  sortFn = defaultSortFn
}: UseDataTableOptions<TData>) {
  // 状态管理
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    pageSize: initialPageSize
  })
  
  const [sort, setSort] = useState<SortState>(initialSort)
  const [filters, setFilters] = useState<Record<string, string>>(initialFilters)
  const [search, setSearch] = useState('')

  // 筛选和排序后的数据
  const processedData = useMemo(() => {
    if (serverSide) return data

    let result = [...data]

    // 筛选
    result = result.filter(item => filterFn(item, filters, search))

    // 排序
    if (sort.direction && sort.column) {
      result.sort((a, b) => sortFn(a, b, sort))
    }

    return result
  }, [data, filters, search, sort, serverSide, filterFn, sortFn])

  // 分页后的数据
  const paginatedData = useMemo(() => {
    if (serverSide) return data

    const startIndex = (pagination.page - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    return processedData.slice(startIndex, endIndex)
  }, [processedData, pagination, serverSide, data])

  // 分页信息
  const paginationInfo = useMemo((): PaginationInfo => {
    const totalItems = serverSide ? data.length : processedData.length
    const totalPages = totalItems > 0 ? Math.ceil(totalItems / pagination.pageSize) : 1

    return {
      currentPage: pagination.page,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages
    }
  }, [processedData.length, pagination, serverSide, data.length])

  // 更新分页
  const updatePagination = useCallback((updates: Partial<PaginationState>) => {
    setPagination(prev => ({ ...prev, ...updates }))
  }, [])

  // 更新排序
  const updateSort = useCallback((newSort: SortState) => {
    setSort(newSort)
    // 排序变化时重置到第一页
    setPagination(prev => ({ ...prev, page: 1 }))
  }, [])

  // 更新筛选
  const updateFilter = useCallback((key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    // 筛选变化时重置到第一页
    setPagination(prev => ({ ...prev, page: 1 }))
  }, [])

  // 更新搜索
  const updateSearch = useCallback((value: string) => {
    setSearch(value)
    // 搜索变化时重置到第一页
    setPagination(prev => ({ ...prev, page: 1 }))
  }, [])

  // 重置所有筛选
  const resetFilters = useCallback(() => {
    setFilters(initialFilters)
    setSearch('')
    setPagination(prev => ({ ...prev, page: 1 }))
  }, [initialFilters])

  // 跳转到指定页
  const goToPage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page: Math.max(1, page) }))
  }, [])

  // 改变页面大小
  const changePageSize = useCallback((newPageSize: number) => {
    setPagination({ page: 1, pageSize: newPageSize })
  }, [])

  return {
    // 数据
    data: paginatedData,
    processedData,
    
    // 状态
    pagination,
    sort,
    filters,
    search,
    paginationInfo,
    
    // 更新函数
    updatePagination,
    updateSort,
    updateFilter,
    updateSearch,
    resetFilters,
    goToPage,
    changePageSize,
    
    // 表格状态
    tableState: {
      pagination,
      sort,
      filters,
      search
    } as DataTableState
  }
}
