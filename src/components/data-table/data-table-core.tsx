import * as React from 'react'
import { ChevronUpIcon, ChevronDownIcon } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { cn } from '@/lib/utils'
import { DataTableColumn, SortDirection, SortState } from './types'

// 排序图标组件
function SortIcon({ direction }: { direction: SortDirection }) {
  if (direction === 'asc') {
    return <ChevronUpIcon className="h-4 w-4" />
  }
  if (direction === 'desc') {
    return <ChevronDownIcon className="h-4 w-4" />
  }
  return (
    <div className="flex flex-col">
      <ChevronUpIcon className="h-3 w-3 opacity-50" />
      <ChevronDownIcon className="h-3 w-3 opacity-50 -mt-1" />
    </div>
  )
}

// 核心表格组件属性
export interface DataTableCoreProps<TData> {
  columns: DataTableColumn<TData>[]
  data: TData[]
  isLoading?: boolean
  emptyMessage?: string
  className?: string
  sortable?: boolean
  onSort?: (sort: SortState) => void
  currentSort?: SortState
}

// 核心表格组件
export function DataTableCore<TData>({
  columns,
  data,
  isLoading = false,
  emptyMessage = 'No data available',
  className,
  sortable = false,
  onSort,
  currentSort
}: DataTableCoreProps<TData>) {
  const handleSort = (columnId: string) => {
    if (!sortable || !onSort) return

    let newDirection: SortDirection = 'asc'
    
    if (currentSort?.column === columnId) {
      if (currentSort.direction === 'asc') {
        newDirection = 'desc'
      } else if (currentSort.direction === 'desc') {
        newDirection = null
      }
    }

    onSort({
      column: columnId,
      direction: newDirection
    })
  }

  const renderCell = (column: DataTableColumn<TData>, item: TData) => {
    if (column.cell) {
      return column.cell(item)
    }
    
    if (column.accessorKey) {
      const value = item[column.accessorKey]
      return value?.toString() || ''
    }
    
    return ''
  }

  if (isLoading) {
    return (
      <div className={cn("rounded-md border", className)}>
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead 
                  key={column.id}
                  className={column.className}
                  style={{ width: column.width }}
                >
                  {typeof column.header === 'string' ? column.header : column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={column.id} className={column.className}>
                    <div className="h-4 bg-muted animate-pulse rounded" />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className={cn("rounded-md border", className)}>
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead 
                key={column.id}
                className={cn(column.className, sortable && column.sortable && "cursor-pointer select-none")}
                style={{ width: column.width }}
                onClick={() => column.sortable && handleSort(column.id)}
              >
                <div className="flex items-center gap-2">
                  {typeof column.header === 'string' ? column.header : column.header}
                  {sortable && column.sortable && (
                    <SortIcon 
                      direction={
                        currentSort?.column === column.id 
                          ? currentSort.direction 
                          : null
                      } 
                    />
                  )}
                </div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell 
                colSpan={columns.length} 
                className="h-24 text-center text-muted-foreground"
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          ) : (
            data.map((item, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell 
                    key={column.id}
                    className={column.className}
                    style={{ width: column.width }}
                  >
                    {renderCell(column, item)}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
