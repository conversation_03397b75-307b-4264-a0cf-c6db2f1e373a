import * as React from 'react'

// 列定义
export interface DataTableColumn<TData> {
  id: string
  header: string | React.ReactNode
  accessorKey?: keyof TData
  cell?: (data: TData) => React.ReactNode
  sortable?: boolean
  className?: string
  width?: string
}

// 排序状态
export type SortDirection = 'asc' | 'desc' | null

export interface SortState {
  column: string
  direction: SortDirection
}

// 分页状态
export interface PaginationState {
  page: number
  pageSize: number
}

// 分页信息
export interface PaginationInfo {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
}

// 筛选配置
export interface FilterOption {
  label: string
  value: string
}

export interface FilterConfig {
  id: string
  title: string
  options: FilterOption[]
}

// 服务端分页信息
export interface ServerPaginationInfo {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
}

// DataTable 属性
export interface DataTableProps<TData> {
  columns: DataTableColumn<TData>[]
  data: TData[]
  emptyMessage?: string
  className?: string
  isLoading?: boolean
  serverSidePagination?: boolean
  
  // 搜索配置
  searchPlaceholder?: string
  searchFields?: (keyof TData)[]
  
  // 筛选配置
  filters?: FilterConfig[]
  
  // 分页配置
  pageSize?: number
  showPagination?: boolean
  
  // 工具栏配置
  showToolbar?: boolean
  toolbarActions?: React.ReactNode
  
  // 服务端分页配置
  serverPaginationInfo?: ServerPaginationInfo
  onServerPageChange?: (page: number) => void
  onServerPageSizeChange?: (pageSize: number) => void
  onServerSearchChange?: (search: string) => void
  onServerFilterChange?: (filterId: string, value: string) => void
  
  // 服务端筛选状态
  serverSearchValue?: string
  serverSelectedFilters?: Record<string, string[]>
}

// Hook 配置
export interface UseDataTableOptions<TData> {
  data: TData[]
  initialPageSize?: number
  initialSort?: SortState
  initialFilters?: Record<string, string>
  serverSide?: boolean
  filterFn?: (item: TData, filters: Record<string, string>, search: string) => boolean
  sortFn?: (a: TData, b: TData, sort: SortState) => number
}

// 表格状态
export interface DataTableState {
  pagination: PaginationState
  sort: SortState
  filters: Record<string, string>
  search: string
}
