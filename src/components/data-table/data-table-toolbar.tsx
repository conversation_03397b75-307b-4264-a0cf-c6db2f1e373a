import { DataTableSearch } from './data-table-search'
import { DataTableFilters } from './data-table-filters'
import { FilterConfig } from './types'

interface DataTableToolbarProps {
  searchValue: string
  onSearchChange: (value: string) => void
  searchPlaceholder?: string
  filters?: FilterConfig[]
  selectedFilters?: Record<string, string[]>
  onFilterChange?: (filterId: string, values: string[]) => void
  onClearFilters?: () => void
  children?: React.ReactNode
}

export function DataTableToolbar({
  searchValue,
  onSearchChange,
  searchPlaceholder = 'Search...',
  filters = [],
  selectedFilters = {},
  onFilterChange,
  onClearFilters,
  children,
}: DataTableToolbarProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <DataTableSearch
          value={searchValue}
          onChange={onSearchChange}
          placeholder={searchPlaceholder}
          className="w-[150px] lg:w-[250px]"
        />
        {filters.length > 0 && onFilterChange && onClearFilters && (
          <DataTableFilters
            filters={filters}
            selectedFilters={selectedFilters}
            onFilterChange={onFilterChange}
            onClearFilters={onClearFilters}
          />
        )}
      </div>
      {children && <div className="flex items-center space-x-2">{children}</div>}
    </div>
  )
}
