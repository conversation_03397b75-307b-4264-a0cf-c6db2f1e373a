import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Eye, EyeOff, Settings, Save, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

export interface AIConfig {
  provider: 'openai' | 'google' | 'anthropic' | 'custom'
  apiKey: string
  model: string
  baseUrl?: string
  temperature?: number
}

interface AIConfigProps {
  onConfigChange: (config: AIConfig) => void
  initialConfig?: AIConfig
}

const DEFAULT_MODELS = {
  openai: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
  google: ['gemini-2.0-flash', 'gemini-1.5-pro', 'gemini-1.5-flash'],
  anthropic: ['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229'],
  custom: []
}

const PROVIDER_LABELS = {
  openai: 'OpenAI',
  google: 'Google Gemini',
  anthropic: 'Anthropic Claude',
  custom: 'Custom API'
}

export function AIConfig({ onConfigChange, initialConfig }: AIConfigProps) {
  const [config, setConfig] = useState<AIConfig>(
    initialConfig || {
      provider: 'openai',
      apiKey: '',
      model: 'gpt-4o-mini',
      temperature: 0.7
    }
  )
  const [showApiKey, setShowApiKey] = useState(false)
  const [isValid, setIsValid] = useState(false)

  // 从localStorage加载配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('ai-assistant-config')
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig)
        setConfig(parsed)
        setIsValid(!!parsed.apiKey)
      } catch (error) {
        console.error('Failed to parse saved config:', error)
      }
    }
  }, [])

  // 验证配置
  useEffect(() => {
    const valid = config.apiKey.trim().length > 0 && config.model.trim().length > 0
    setIsValid(valid)
    if (valid) {
      onConfigChange(config)
    }
  }, [config, onConfigChange])

  const handleProviderChange = (provider: AIConfig['provider']) => {
    const newConfig = {
      ...config,
      provider,
      model: DEFAULT_MODELS[provider][0] || '',
      baseUrl: provider === 'custom' ? config.baseUrl || '' : undefined
    }
    setConfig(newConfig)
  }

  const handleSaveConfig = () => {
    localStorage.setItem('ai-assistant-config', JSON.stringify(config))
    // 可以添加保存成功的提示
  }

  const getApiKeyPlaceholder = () => {
    switch (config.provider) {
      case 'openai':
        return 'sk-...'
      case 'google':
        return 'AIza...'
      case 'anthropic':
        return 'sk-ant-...'
      case 'custom':
        return '输入您的API密钥'
      default:
        return '输入API密钥'
    }
  }

  const getProviderDocs = () => {
    switch (config.provider) {
      case 'openai':
        return 'https://platform.openai.com/api-keys'
      case 'google':
        return 'https://aistudio.google.com/app/apikey'
      case 'anthropic':
        return 'https://console.anthropic.com/settings/keys'
      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          AI助手配置
        </CardTitle>
        <CardDescription>
          配置您的AI服务提供商和API密钥以开始使用AI助手
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 服务提供商选择 */}
        <div className="space-y-2">
          <Label htmlFor="provider">AI服务提供商</Label>
          <Select value={config.provider} onValueChange={handleProviderChange}>
            <SelectTrigger>
              <SelectValue placeholder="选择AI服务提供商" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(PROVIDER_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* API密钥输入 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="apiKey">API密钥</Label>
            {getProviderDocs() && (
              <Button
                variant="link"
                size="sm"
                asChild
                className="h-auto p-0 text-xs"
              >
                <a href={getProviderDocs()} target="_blank" rel="noopener noreferrer">
                  获取API密钥
                </a>
              </Button>
            )}
          </div>
          <div className="relative">
            <Input
              id="apiKey"
              type={showApiKey ? 'text' : 'password'}
              placeholder={getApiKeyPlaceholder()}
              value={config.apiKey}
              onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowApiKey(!showApiKey)}
            >
              {showApiKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* 模型选择 */}
        <div className="space-y-2">
          <Label htmlFor="model">模型</Label>
          <Select value={config.model} onValueChange={(model) => setConfig({ ...config, model })}>
            <SelectTrigger>
              <SelectValue placeholder="选择模型" />
            </SelectTrigger>
            <SelectContent>
              {DEFAULT_MODELS[config.provider].map((model) => (
                <SelectItem key={model} value={model}>
                  {model}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 自定义API端点 (仅当选择custom时显示) */}
        {config.provider === 'custom' && (
          <div className="space-y-2">
            <Label htmlFor="baseUrl">API端点</Label>
            <Input
              id="baseUrl"
              placeholder="https://api.example.com/v1"
              value={config.baseUrl || ''}
              onChange={(e) => setConfig({ ...config, baseUrl: e.target.value })}
            />
          </div>
        )}

        {/* 温度设置 */}
        <div className="space-y-2">
          <Label htmlFor="temperature">温度 (创造性)</Label>
          <div className="flex items-center space-x-2">
            <Input
              id="temperature"
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={config.temperature || 0.7}
              onChange={(e) => setConfig({ ...config, temperature: parseFloat(e.target.value) })}
              className="w-20"
            />
            <span className="text-sm text-muted-foreground">
              0 = 精确, 2 = 创造性
            </span>
          </div>
        </div>

        {/* 状态指示 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant={isValid ? 'default' : 'secondary'}>
              {isValid ? '配置有效' : '配置无效'}
            </Badge>
          </div>
          <Button onClick={handleSaveConfig} size="sm" className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            保存配置
          </Button>
        </div>

        {/* 警告信息 */}
        {!isValid && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              请填写API密钥和选择模型以启用AI助手功能
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
