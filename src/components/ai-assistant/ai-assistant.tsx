import React, { useState, useEffect } from 'react'
import { Assistant<PERSON>untimeProvider, useLocalRunt<PERSON>, type Chat<PERSON><PERSON>lAdapter } from "@assistant-ui/react"

import { Thread } from "./thread"
import { AIConfig } from "./ai-config"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MessageSquare, Settings, Bot } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

// 导入样式
import "@assistant-ui/styles/index.css"
import "@assistant-ui/styles/markdown.css"

export interface AIAssistantProps {
  className?: string
}

export function AIAssistant({ className }: AIAssistantProps) {
  const [config, setConfig] = useState<any>(null)
  const [isConfigured, setIsConfigured] = useState(false)
  const [activeTab, setActiveTab] = useState<'chat' | 'config'>('config')

  // 从localStorage加载配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('ai-assistant-config')
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig)
        if (parsed.apiKey && parsed.model) {
          setConfig(parsed)
          setIsConfigured(true)
          setActiveTab('chat')
        }
      } catch (error) {
        console.error('Failed to parse saved config:', error)
      }
    }
  }, [])

  // 创建AI运行时
  const runtime = useLocalRuntime(config ? createChatAdapter(config) : null)

  const handleConfigChange = (newConfig: any) => {
    setConfig(newConfig)
    const isValid = newConfig.apiKey && newConfig.model
    setIsConfigured(isValid)
    if (isValid && activeTab === 'config') {
      setActiveTab('chat')
    }
  }

  if (!isConfigured) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI助手
          </CardTitle>
          <CardDescription>
            请先配置AI服务以开始使用助手功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AIConfig onConfigChange={handleConfigChange} />
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'chat' | 'config')}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Bot className="h-6 w-6" />
            AI助手
          </h2>
          <TabsList>
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              聊天
            </TabsTrigger>
            <TabsTrigger value="config" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              配置
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="chat" className="mt-0">
          {runtime ? (
            <AssistantRuntimeProvider runtime={runtime}>
              <div className="h-[600px] border rounded-lg overflow-hidden">
                <Thread />
              </div>
            </AssistantRuntimeProvider>
          ) : (
            <Alert>
              <AlertDescription>
                正在初始化AI助手，请稍候...
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="config" className="mt-0">
          <AIConfig 
            onConfigChange={handleConfigChange} 
            initialConfig={config}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// 创建聊天适配器函数
function createChatAdapter(config: any): ChatModelAdapter {
  return {
    async *run({ messages, abortSignal }) {
      try {
        const response = await fetch(getApiUrl(config), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`,
          },
          body: JSON.stringify({
            model: config.model,
            messages: [
              {
                role: 'system',
                content: `你是一个有用的AI助手。你正在帮助用户使用一个包含以下功能的管理系统：

1. 仪表板 - 显示系统概览和统计信息
2. MQTT管理 - 管理MQTT连接和消息
3. 用户管理 - 管理系统用户和权限
4. 设备管理 - 管理连接的设备
5. 监控功能 - 系统监控和日志
6. 帮助中心 - 用户支持和文档

请用中文回答用户的问题，并尽可能提供有用和准确的信息。如果用户询问系统功能，请基于上述功能列表进行回答。`
              },
              ...messages.map(m => ({
                role: m.role,
                content: m.content.filter(c => c.type === 'text').map(c => c.text).join('\n')
              }))
            ],
            temperature: config.temperature || 0.7,
            stream: true,
          }),
          signal: abortSignal,
        })

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`)
        }

        const reader = response.body?.getReader()
        if (!reader) {
          throw new Error('No response body')
        }

        const decoder = new TextDecoder()
        let text = ''

        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6)
                if (data === '[DONE]') return

                try {
                  const parsed = JSON.parse(data)
                  const content = parsed.choices?.[0]?.delta?.content
                  if (content) {
                    text += content
                    yield {
                      content: [{ type: 'text', text }],
                    }
                  }
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }
          }
        } finally {
          reader.releaseLock()
        }
      } catch (error) {
        console.error('Chat API error:', error)
        throw error
      }
    }
  }
}

function getApiUrl(config: any) {
  switch (config.provider) {
    case 'openai':
      return 'https://api.openai.com/v1/chat/completions'
    case 'google':
      // Google Gemini API uses a different format, this is a simplified implementation
      // In production, you might want to use the official Google AI SDK
      return `https://generativelanguage.googleapis.com/v1beta/models/${config.model}:generateContent`
    case 'anthropic':
      return 'https://api.anthropic.com/v1/messages'
    case 'custom':
      return `${config.baseUrl}/chat/completions`
    default:
      throw new Error(`Unsupported provider: ${config.provider}`)
  }
}
