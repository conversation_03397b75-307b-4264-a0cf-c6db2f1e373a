import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIAssistant } from '../ai-assistant'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock fetch
global.fetch = vi.fn()

describe('AIAssistant', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  it('should render configuration form when not configured', () => {
    render(<AIAssistant />)
    
    expect(screen.getByText('AI助手')).toBeInTheDocument()
    expect(screen.getByText('请先配置AI服务以开始使用助手功能')).toBeInTheDocument()
    expect(screen.getByText('AI服务提供商')).toBeInTheDocument()
  })

  it('should load saved configuration from localStorage', () => {
    const savedConfig = {
      provider: 'openai',
      apiKey: 'test-api-key',
      model: 'gpt-3.5-turbo',
      temperature: 0.7
    }
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedConfig))

    render(<AIAssistant />)
    
    // Should show chat interface when configured
    expect(screen.getByText('聊天')).toBeInTheDocument()
    expect(screen.getByText('配置')).toBeInTheDocument()
  })

  it('should switch between chat and config tabs', async () => {
    const savedConfig = {
      provider: 'openai',
      apiKey: 'test-api-key',
      model: 'gpt-3.5-turbo'
    }
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedConfig))

    render(<AIAssistant />)
    
    // Should start on chat tab when configured
    expect(screen.getByRole('tabpanel')).toBeInTheDocument()
    
    // Click config tab
    fireEvent.click(screen.getByText('配置'))
    
    await waitFor(() => {
      expect(screen.getByText('AI服务提供商')).toBeInTheDocument()
    })
  })

  it('should handle configuration changes', async () => {
    render(<AIAssistant />)
    
    // Select OpenAI provider
    const providerSelect = screen.getByRole('combobox')
    fireEvent.click(providerSelect)
    
    await waitFor(() => {
      const openaiOption = screen.getByText('OpenAI')
      fireEvent.click(openaiOption)
    })

    // Enter API key
    const apiKeyInput = screen.getByPlaceholder(/输入您的.*API密钥/)
    fireEvent.change(apiKeyInput, { target: { value: 'test-api-key' } })

    // Select model
    const modelSelect = screen.getAllByRole('combobox')[1]
    fireEvent.click(modelSelect)
    
    await waitFor(() => {
      const modelOption = screen.getByText('gpt-3.5-turbo')
      fireEvent.click(modelOption)
    })

    // Save configuration
    const saveButton = screen.getByText('保存配置')
    fireEvent.click(saveButton)

    // Should save to localStorage
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'ai-assistant-config',
      expect.stringContaining('test-api-key')
    )
  })

  it('should validate API key format', () => {
    render(<AIAssistant />)
    
    // Enter invalid API key
    const apiKeyInput = screen.getByPlaceholder(/输入您的.*API密钥/)
    fireEvent.change(apiKeyInput, { target: { value: 'invalid-key' } })
    
    // Should show validation error
    expect(screen.getByText(/API密钥格式不正确/)).toBeInTheDocument()
  })

  it('should show different placeholders for different providers', async () => {
    render(<AIAssistant />)
    
    // Test OpenAI placeholder
    const providerSelect = screen.getByRole('combobox')
    fireEvent.click(providerSelect)
    
    await waitFor(() => {
      const openaiOption = screen.getByText('OpenAI')
      fireEvent.click(openaiOption)
    })
    
    expect(screen.getByPlaceholder('输入您的OpenAI API密钥')).toBeInTheDocument()
    
    // Test Google placeholder
    fireEvent.click(providerSelect)
    
    await waitFor(() => {
      const googleOption = screen.getByText('Google Gemini')
      fireEvent.click(googleOption)
    })
    
    expect(screen.getByPlaceholder('输入您的Google API密钥')).toBeInTheDocument()
  })
})
