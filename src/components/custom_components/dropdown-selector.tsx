import React from 'react'
import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export interface DropdownSelectorOption {
  key: string
  label: string
  value: string
  icon?: React.ReactNode
  prefix?: React.ReactNode
  suffix?: React.ReactNode
}

export interface DropdownSelectorProps {
  options: DropdownSelectorOption[]
  value: string
  onValueChange: (value: string) => void
  triggerIcon?: LucideIcon | React.ComponentType
  triggerContent?: React.ReactNode
  placeholder?: string
  className?: string
  triggerClassName?: string
  contentClassName?: string
  modal?: boolean
  align?: 'start' | 'center' | 'end'
  side?: 'top' | 'right' | 'bottom' | 'left'
  showCheck?: boolean
  checkIcon?: React.ComponentType<{ className?: string }>
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

export function DropdownSelector({
  options,
  value,
  onValueChange,
  triggerIcon: TriggerIcon,
  triggerContent,
  placeholder,
  className,
  triggerClassName,
  contentClassName,
  modal = false,
  align = 'end',
  side = 'bottom',
  showCheck = false,
  checkIcon: CheckIcon,
  variant = 'ghost',
  size = 'icon',
}: DropdownSelectorProps) {
  const selectedOption = options.find(option => option.value === value)

  const renderTrigger = () => {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn(
          size === 'icon' && 'h-8 w-8',
          variant === 'ghost' && size === 'icon' && 'scale-95 rounded-full',
          triggerClassName
        )}
      >
        {triggerContent ? (
          triggerContent
        ) : (
          <>
            {TriggerIcon && <TriggerIcon className="h-4 w-4" />}
            {placeholder && !TriggerIcon && (
              <span className="sr-only">{placeholder}</span>
            )}
          </>
        )}
      </Button>
    )
  }

  return (
    <div className={className}>
      <DropdownMenu modal={modal}>
        <DropdownMenuTrigger asChild>
          {renderTrigger()}
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align={align} 
          side={side}
          className={contentClassName}
        >
          {options.map((option) => (
            <DropdownMenuItem
              key={option.key}
              onClick={() => onValueChange(option.value)}
              className={cn(
                'flex items-center gap-2',
                value === option.value && 'bg-accent'
              )}
            >
              {option.prefix}
              {option.icon}
              <span>{option.label}</span>
              {option.suffix}
              {showCheck && CheckIcon && value === option.value && (
                <CheckIcon className="ml-auto h-4 w-4" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
