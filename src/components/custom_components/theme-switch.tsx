import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { IconCheck, IconMoon, IconSun } from '@tabler/icons-react'
import { useTheme } from '@/context/theme-context'
import { DropdownSelector, DropdownSelectorOption } from './dropdown-selector'

export function ThemeSwitch() {
  const { theme, setTheme } = useTheme()
  const { t } = useTranslation()

  /* Update theme-color meta tag
   * when theme is updated */
  useEffect(() => {
    const themeColor = theme === 'dark' ? '#020817' : '#fff'
    const metaThemeColor = document.querySelector("meta[name='theme-color']")
    if (metaThemeColor) metaThemeColor.setAttribute('content', themeColor)
  }, [theme])

  const themeOptions: DropdownSelectorOption[] = [
    {
      key: 'light',
      label: t('common.theme.light'),
      value: 'light',
    },
    {
      key: 'dark',
      label: t('common.theme.dark'),
      value: 'dark',
    },
    {
      key: 'system',
      label: t('common.theme.system'),
      value: 'system',
    },
  ]

  const triggerContent = (
    <>
      <IconSun className='size-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90' />
      <IconMoon className='absolute size-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0' />
      <span className='sr-only'>Toggle theme</span>
    </>
  )

  return (
    <DropdownSelector
      options={themeOptions}
      value={theme}
      onValueChange={setTheme}
      triggerContent={triggerContent}
      showCheck={true}
      checkIcon={IconCheck}
      modal={false}
    />
  )
}
