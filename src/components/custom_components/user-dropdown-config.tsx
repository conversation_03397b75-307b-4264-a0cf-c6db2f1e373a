import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Set<PERSON><PERSON> } from 'lucide-react'
import { UserDropdownMenuItem } from './user-dropdown'

// 侧边栏用户菜单配置
export const sidebarUserMenuItems: UserDropdownMenuItem[] = [
  {
    key: 'account',
    label: 'sidebar.user.account',
    href: '/settings/account',
    icon: BadgeCheck,
  },
  {
    key: 'settings',
    label: 'sidebar.user.settings',
    href: '/settings',
    icon: Settings,
  },
]

// 头部用户菜单配置
export const headerUserMenuItems: UserDropdownMenuItem[] = [
  {
    key: 'profile',
    label: 'Profile',
    href: '/settings',
    shortcut: '⇧⌘P',
  },
  {
    key: 'settings',
    label: 'Settings',
    href: '/settings',
    shortcut: '⌘S',
  },
  {
    key: 'account',
    label: 'Account',
    href: '/settings/account',
    shortcut: '⌘A',
  },
]
