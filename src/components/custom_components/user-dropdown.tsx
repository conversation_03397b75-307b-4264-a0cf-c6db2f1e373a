import React from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ChevronsUpDown, LucideIcon } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'

export interface UserDropdownMenuItem {
  key: string
  label: string
  href?: string
  icon?: LucideIcon
  shortcut?: string
  onClick?: () => void
  separator?: boolean
}

export interface UserDropdownProps {
  user: {
    name: string
    email: string
    avatar: string
  }
  variant: 'sidebar' | 'header'
  menuItems?: UserDropdownMenuItem[]
  onLogout?: () => void
  className?: string
  modal?: boolean
}

export function UserDropdown({
  user,
  variant = 'header',
  menuItems = [],
  onLogout,
  className,
  modal = false,
}: UserDropdownProps) {
  const { isMobile } = useSidebar()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { logout, user: authUser } = useAuthStore()

  const handleLogout = () => {
    if (onLogout) {
      onLogout()
    } else {
      logout()
      navigate('/sign-in')
    }
  }

  // 使用认证用户信息，如果没有则使用传入的默认用户信息
  const displayUser = authUser ? {
    name: authUser.profile?.display_name || authUser.email.split('@')[0],
    email: authUser.email,
    avatar: authUser.profile?.avatar || user.avatar
  } : user

  const renderTrigger = () => {
    if (variant === 'sidebar') {
      return (
        <SidebarMenuButton
          size='lg'
          className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
        >
          <Avatar className='h-8 w-8 rounded-lg'>
            <AvatarImage src={displayUser.avatar} alt={displayUser.name} />
            <AvatarFallback className='rounded-lg'>
              {displayUser.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className='grid flex-1 text-left text-sm leading-tight'>
            <span className='truncate font-semibold'>{displayUser.name}</span>
            <span className='truncate text-xs'>{displayUser.email}</span>
          </div>
          <ChevronsUpDown className='ml-auto size-4' />
        </SidebarMenuButton>
      )
    }

    return (
      <Button variant='ghost' className='relative h-8 w-8 rounded-full'>
        <Avatar className='h-8 w-8'>
          <AvatarImage src={displayUser.avatar} alt={displayUser.name} />
          <AvatarFallback>
            {displayUser.name.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      </Button>
    )
  }

  const renderMenuContent = () => {
    const contentProps = variant === 'sidebar' 
      ? {
          className: 'w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg',
          side: (isMobile ? 'bottom' : 'right') as 'bottom' | 'right',
          align: 'end' as const,
          sideOffset: 4,
        }
      : {
          className: 'w-56',
          align: 'end' as const,
          forceMount: true,
        }

    return (
      <DropdownMenuContent {...contentProps}>
        <DropdownMenuLabel className={variant === 'sidebar' ? 'p-0 font-normal' : 'font-normal'}>
          {variant === 'sidebar' ? (
            <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
              <Avatar className='h-8 w-8 rounded-lg'>
                <AvatarImage src={displayUser.avatar} alt={displayUser.name} />
                <AvatarFallback className='rounded-lg'>
                  {displayUser.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>{displayUser.name}</span>
                <span className='truncate text-xs'>{displayUser.email}</span>
              </div>
            </div>
          ) : (
            <div className='flex flex-col space-y-1'>
              <p className='text-sm leading-none font-medium'>{displayUser.name}</p>
              <p className='text-muted-foreground text-xs leading-none'>
                {displayUser.email}
              </p>
            </div>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {menuItems.length > 0 && (
          <>
            <DropdownMenuGroup>
              {menuItems.map((item) => {
                if (item.separator) {
                  return <DropdownMenuSeparator key={item.key} />
                }

                const ItemIcon = item.icon

                if (item.href) {
                  return (
                    <DropdownMenuItem key={item.key} asChild>
                      <Link to={item.href}>
                        {ItemIcon && <ItemIcon />}
                        {item.label.startsWith('sidebar.') ? t(item.label) : item.label}
                        {item.shortcut && (
                          <DropdownMenuShortcut>{item.shortcut}</DropdownMenuShortcut>
                        )}
                      </Link>
                    </DropdownMenuItem>
                  )
                }

                return (
                  <DropdownMenuItem key={item.key} onClick={item.onClick}>
                    {ItemIcon && <ItemIcon />}
                    {item.label.startsWith('sidebar.') ? t(item.label) : item.label}
                    {item.shortcut && (
                      <DropdownMenuShortcut>{item.shortcut}</DropdownMenuShortcut>
                    )}
                  </DropdownMenuItem>
                )
              })}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
          </>
        )}
        
        <DropdownMenuItem onClick={handleLogout}>
          {t('sidebar.user.logout')}
          {variant === 'header' && (
            <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    )
  }

  const dropdownMenu = (
    <DropdownMenu modal={modal}>
      <DropdownMenuTrigger asChild>
        {renderTrigger()}
      </DropdownMenuTrigger>
      {renderMenuContent()}
    </DropdownMenu>
  )

  if (variant === 'sidebar') {
    return (
      <SidebarMenu className={className}>
        <SidebarMenuItem>
          {dropdownMenu}
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return dropdownMenu
}
