import { useEffect, useRef, useState } from 'react'
import { useLocation } from 'react-router-dom'
import LoadingBar, { LoadingBarRef } from 'react-top-loading-bar'

export function NavigationProgress() {
  const ref = useRef<LoadingBarRef>(null)
  const location = useLocation()
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // 简化的导航进度：在路由变化时显示加载条
    setIsLoading(true)
    ref.current?.continuousStart()

    const timer = setTimeout(() => {
      setIsLoading(false)
      ref.current?.complete()
    }, 300) // 300ms 后完成加载动画

    return () => clearTimeout(timer)
  }, [location.pathname])

  return (
    <LoadingBar
      color='var(--primary)'
      ref={ref}
      shadow={true}
      height={2}
    />
  )
}
