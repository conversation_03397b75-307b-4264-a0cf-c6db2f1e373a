import { useTranslation } from 'react-i18next'
import { Languages } from 'lucide-react'
import { DropdownSelector, DropdownSelectorOption } from './dropdown-selector'

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
]

export function LanguageSwitch() {
  const { i18n, t } = useTranslation()

  const changeLanguage = (languageCode: string) => {
    i18n.changeLanguage(languageCode)
  }

  const languageOptions: DropdownSelectorOption[] = languages.map(lang => ({
    key: lang.code,
    label: lang.name,
    value: lang.code,
    prefix: <span className="text-lg">{lang.flag}</span>,
  }))

  return (
    <DropdownSelector
      options={languageOptions}
      value={i18n.language}
      onValueChange={changeLanguage}
      triggerIcon={Languages}
      placeholder={t('common.language.switch')}
      className="h-8 w-8"
    />
  )
}
