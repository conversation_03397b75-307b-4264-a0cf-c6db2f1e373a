import { useTranslation } from 'react-i18next'
import { useAuthStore } from '@/stores/authStore'
import { canAccessAdminPages } from '@/utils/auth-utils'
import {
  SidebarData,
  SidebarDataConfig,
  NavGroup,
  NavGroupConfig,
  NavItem,
  NavItemConfig,
} from '../types'

export function useSidebarData(config: SidebarDataConfig): SidebarData {
  const { t } = useTranslation()
  const { user } = useAuthStore()

  const transformNavItem = (itemConfig: NavItemConfig): NavItem => {
    if ('items' in itemConfig && itemConfig.items) {
      return {
        title: t(itemConfig.titleKey),
        badge: itemConfig.badge,
        icon: itemConfig.icon,
        items: itemConfig.items.map(subItem => ({
          title: t(subItem.titleKey),
          url: subItem.url,
          badge: subItem.badge,
          icon: subItem.icon,
        })),
      }
    } else {
      return {
        title: t(itemConfig.titleKey),
        badge: itemConfig.badge,
        icon: itemConfig.icon,
        url: (itemConfig as any).url,
      }
    }
  }

  const transformNavGroup = (groupConfig: NavGroupConfig): NavGroup => ({
    title: t(groupConfig.titleKey),
    items: groupConfig.items.map(transformNavItem),
  })

  // 过滤需要管理员权限的导航组
  const filteredNavGroups = config.navGroups.filter(groupConfig => {
    // 如果该组需要管理员权限，检查用户是否为管理员
    if (groupConfig.requiresAdmin) {
      return canAccessAdminPages(user)
    }
    // 否则显示该组
    return true
  })

  return {
    user: config.user,
    navGroups: filteredNavGroups.map(transformNavGroup),
  }
}
