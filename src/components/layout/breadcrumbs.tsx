// import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { IconChevronRight, IconHome } from '@tabler/icons-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  title: string
  href?: string
  isActive?: boolean
}

interface BreadcrumbsProps {
  className?: string
  items?: BreadcrumbItem[]
  showHome?: boolean
}

export function Breadcrumbs({
  className,
  items: customItems,
  showHome = true
}: BreadcrumbsProps) {
  const { t } = useTranslation()
  const location = useLocation()

  // 生成默认面包屑项目（基于当前路由）
  const generateBreadcrumbsFromRoute = (): BreadcrumbItem[] => {
    const pathname = location.pathname
    const segments = pathname.split('/').filter(Boolean)
    
    const breadcrumbs: BreadcrumbItem[] = []
    
    // 添加首页
    if (showHome) {
      breadcrumbs.push({
        title: t('sidebar.nav.dashboard'),
        href: '/',
        isActive: pathname === '/'
      })
    }
    
    // 根据路径段生成面包屑
    let currentPath = ''
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`
      const isLast = index === segments.length - 1
      
      // 获取翻译键
      const titleKey = getBreadcrumbTitleKey(segment)
      
      breadcrumbs.push({
        title: t(titleKey),
        href: isLast ? undefined : currentPath,
        isActive: isLast
      })
    })
    
    return breadcrumbs
  }
  
  // 获取面包屑标题的翻译键
  const getBreadcrumbTitleKey = (segment: string): string => {
    const keyMap: Record<string, string> = {
      'tasks': 'sidebar.nav.tasks',
      'users': 'sidebar.nav.users',
      'apps': 'sidebar.nav.apps',
      'settings': 'sidebar.nav.settings',
      'account': 'settings.account.title',
      'appearance': 'settings.appearance.title',
      'notifications': 'settings.notifications.title',
      'help-center': 'sidebar.nav.helpCenter'
    }
    
    return keyMap[segment] || segment
  }
  
  // 使用自定义项目或生成的项目
  const breadcrumbItems = customItems || generateBreadcrumbsFromRoute()
  
  if (breadcrumbItems.length <= 1) {
    return null
  }
  
  return (
    <nav 
      className={cn('flex items-center space-x-1 text-sm', className)}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-1">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <IconChevronRight 
                className="mx-2 h-4 w-4 text-muted-foreground" 
                aria-hidden="true" 
              />
            )}
            
            {item.href && !item.isActive ? (
              <Link
                to={item.href}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                {index === 0 && showHome ? (
                  <span className="flex items-center">
                    <IconHome className="mr-1 h-4 w-4" />
                    {item.title}
                  </span>
                ) : (
                  item.title
                )}
              </Link>
            ) : (
              <span 
                className={cn(
                  'font-medium',
                  item.isActive ? 'text-foreground' : 'text-muted-foreground'
                )}
                aria-current={item.isActive ? 'page' : undefined}
              >
                {index === 0 && showHome ? (
                  <span className="flex items-center">
                    <IconHome className="mr-1 h-4 w-4" />
                    {item.title}
                  </span>
                ) : (
                  item.title
                )}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}
