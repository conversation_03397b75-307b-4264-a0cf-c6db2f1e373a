// import { LinkProps } from 'react-router-dom'

interface User {
  name: string
  email: string
  avatar: string
}

interface BaseNavItem {
  title: string
  badge?: string
  icon?: React.ElementType
}

interface BaseNavItemConfig {
  titleKey: string
  badge?: string
  icon?: React.ElementType
}

type NavLink = BaseNavItem & {
  url: string
  items?: never
}

type NavLinkConfig = BaseNavItemConfig & {
  url: string
  items?: never
}

type NavCollapsible = BaseNavItem & {
  items: (BaseNavItem & { url: string })[]
  url?: never
}

type NavCollapsibleConfig = BaseNavItemConfig & {
  items: (BaseNavItemConfig & { url: string })[]
  url?: never
}

type NavItem = NavCollapsible | NavLink
type NavItemConfig = NavCollapsibleConfig | NavLinkConfig

interface NavGroup {
  title: string
  items: NavItem[]
}

interface NavGroupConfig {
  titleKey: string
  items: NavItemConfig[]
  requiresAdmin?: boolean
}

interface SidebarData {
  user: User
  navGroups: NavGroup[]
}

interface SidebarDataConfig {
  user: User
  navGroups: NavGroupConfig[]
}

export type {
  SidebarData,
  SidebarDataConfig,
  NavGroup,
  NavGroupConfig,
  NavItem,
  NavItemConfig,
  NavCollapsible,
  NavCollapsibleConfig,
  NavLink,
  NavLinkConfig,
}
