import React from 'react'
import { cn } from '@/lib/utils'
import { DashboardHeader } from './dashboard-header'
import { Main } from './main'

interface BreadcrumbItem {
  title: string
  href?: string
  isActive?: boolean
}

interface PageHeaderProps {
  title?: string
  description?: string
  actions?: React.ReactNode
}

interface DashboardLayoutProps {
  children: React.ReactNode

  // Header配置
  headerFixed?: boolean
  showBreadcrumbs?: boolean
  breadcrumbItems?: BreadcrumbItem[]
  headerRightContent?: React.ReactNode

  // Main配置
  mainFixed?: boolean
  mainClassName?: string

  // 页面头部配置
  pageHeader?: PageHeaderProps

  // 其他配置
  className?: string
}

export function DashboardLayout({
  children,

  // Header配置
  headerFixed = false,
  showBreadcrumbs = true,
  breadcrumbItems,
  headerRightContent,

  // Main配置
  mainFixed = false,
  mainClassName,

  // 页面头部配置
  pageHeader,

  // 其他配置
  className
}: DashboardLayoutProps) {
  return (
    <div className={cn('flex h-svh flex-col', className)}>
      {/* Dashboard Header */}
      <DashboardHeader
        fixed={headerFixed}
        showBreadcrumbs={showBreadcrumbs}
        breadcrumbItems={breadcrumbItems}
        rightContent={headerRightContent}
      />

      {/* Main Content */}
      <Main 
        fixed={mainFixed}
        className={cn(mainClassName)}
      >
        {/* 页面头部（标题、描述、操作按钮） */}
        {pageHeader && (
          <div className='mb-6 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
            <div>
              {pageHeader.title && (
                <h1 className='text-2xl font-bold tracking-tight md:text-3xl'>
                  {pageHeader.title}
                </h1>
              )}
              {pageHeader.description && (
                <p className='text-muted-foreground mt-1'>
                  {pageHeader.description}
                </p>
              )}
            </div>
            {pageHeader.actions && (
              <div className='flex items-center space-x-2'>
                {pageHeader.actions}
              </div>
            )}
          </div>
        )}

        {/* 页面内容 */}
        {children}
      </Main>
    </div>
  )
}

DashboardLayout.displayName = 'DashboardLayout'
