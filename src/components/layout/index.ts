// Layout components
export { AuthenticatedLayout } from './authenticated-layout'
export { Header } from './header'
export { Main } from './main'
export { TopNav } from './top-nav'
export { AppSidebar } from './app-sidebar'

// New dashboard layout components
export { DashboardLayout } from './dashboard-layout'
export { DashboardHeader } from './dashboard-header'
export { Breadcrumbs } from './breadcrumbs'

// Navigation components
export { NavGroup } from './nav-group'

// Data and utilities
export { sidebarDataConfig } from './data/sidebar-data'
export { useSidebarData } from './utils/sidebar-utils'

// Types
export type {
  SidebarData,
  SidebarDataConfig,
  NavGroup as NavGroupType,
  NavGroupConfig,
  NavItem,
  NavItemConfig,
  NavCollapsible,
  NavCollapsibleConfig,
  NavLink,
  NavLinkConfig,
} from './types'
