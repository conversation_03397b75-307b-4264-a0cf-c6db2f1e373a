import {
  Sidebar,
  <PERSON>bar<PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'
import { NavGroup } from '@/components/layout/nav-group'
import { UserDropdown } from '@/components/custom_components/user-dropdown'
import { sidebarUserMenuItems } from '@/components/custom_components/user-dropdown-config'
import { sidebarDataConfig } from './data/sidebar-data'
import { useSidebarData } from './utils/sidebar-utils'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const sidebarData = useSidebarData(sidebarDataConfig)

  return (
    <Sidebar collapsible='icon' variant='floating' {...props}>
      <SidebarHeader>
        <div className='flex items-center justify-center'>
          <img src='/logo.png' alt='Beacon Cloud Platform'/>
        </div>
      </SidebarHeader>
      <SidebarContent>
        {sidebarData.navGroups.map((props) => (
          <NavGroup key={props.title} {...props} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        <UserDropdown
          user={sidebarData.user}
          variant="sidebar"
          menuItems={sidebarUserMenuItems}
        />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
