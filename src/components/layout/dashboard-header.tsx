import React from 'react'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Search } from '@/components/custom_components/search'
import { ThemeSwitch } from '@/components/custom_components/theme-switch'
import { LanguageSwitch } from '@/components/custom_components/language-switch'
import { UserDropdown } from '@/components/custom_components/user-dropdown'
import { headerUserMenuItems } from '@/components/custom_components/user-dropdown-config'
import { sidebarDataConfig } from './data/sidebar-data'
import { Breadcrumbs } from './breadcrumbs'

interface BreadcrumbItem {
  title: string
  href?: string
  isActive?: boolean
}

interface DashboardHeaderProps extends React.HTMLAttributes<HTMLElement> {
  fixed?: boolean
  showBreadcrumbs?: boolean
  breadcrumbItems?: BreadcrumbItem[]
  rightContent?: React.ReactNode
}

export const DashboardHeader = ({
  className,
  fixed,
  showBreadcrumbs = true,
  breadcrumbItems,
  rightContent,
  ...props
}: DashboardHeaderProps) => {
  const [offset, setOffset] = React.useState(0)

  React.useEffect(() => {
    const onScroll = () => {
      setOffset(document.body.scrollTop || document.documentElement.scrollTop)
    }

    // Add scroll listener to the body
    document.addEventListener('scroll', onScroll, { passive: true })

    // Clean up the event listener on unmount
    return () => document.removeEventListener('scroll', onScroll)
  }, [])

  return (
    <header
      className={cn(
        'bg-background flex h-16 items-center gap-3 p-4 sm:gap-4 w-full shadow-none',
        offset > 10 && fixed ? 'shadow-sm' : 'shadow-none',
        className
      )}
      {...props}
    >
      <SidebarTrigger variant='outline' className='scale-125 sm:scale-100' />
      <Separator orientation='vertical' className='h-6' />

      {/* 面包屑导航 - 左侧显示 */}
      {showBreadcrumbs && (
        <Breadcrumbs
          items={breadcrumbItems}
          className="hidden md:flex"
        />
      )}

      {/* 右侧操作区域 - 统一包含搜索、主题切换、语言切换、用户头像 */}
      <div className='ml-auto flex items-center space-x-4'>
        <Search />
        <ThemeSwitch />
        <LanguageSwitch />
        <UserDropdown
          user={sidebarDataConfig.user}
          variant="header"
          menuItems={headerUserMenuItems}
        />
        {rightContent}
      </div>
    </header>
  )
}

DashboardHeader.displayName = 'DashboardHeader'
