import {
  Pagin<PERSON>,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { useState, useCallback } from 'react'

// 服务端分页接口
export interface ServerPaginationProps {
  mode: 'server'
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  onPageChange: (page: number) => void
  isLoading?: boolean
}

// 客户端分页接口
export interface ClientPaginationProps {
  mode: 'client'
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  onPageChange: (page: number) => void
  isLoading?: boolean
}

export type DataTablePaginationProps = ServerPaginationProps | ClientPaginationProps

// 生成页码数组的辅助函数
function generatePageNumbers(currentPage: number, totalPages: number): (number | 'ellipsis')[] {
  const pages: (number | 'ellipsis')[] = []
  
  if (totalPages <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i)
    }
  } else {
    // 总是显示第一页
    pages.push(1)
    
    if (currentPage <= 4) {
      // 当前页在前面时
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('ellipsis')
      pages.push(totalPages)
    } else if (currentPage >= totalPages - 3) {
      // 当前页在后面时
      pages.push('ellipsis')
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间时
      pages.push('ellipsis')
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i)
      }
      pages.push('ellipsis')
      pages.push(totalPages)
    }
  }
  
  return pages
}

export function DataTablePagination(props: DataTablePaginationProps) {
  const { currentPage, totalPages, totalItems, pageSize, onPageChange, isLoading = false } = props

  // 页面跳转状态
  const [jumpValue, setJumpValue] = useState('')
  const [jumpError, setJumpError] = useState('')

  // 计算显示的条目范围
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalItems)

  // 生成页码
  const pageNumbers = generatePageNumbers(currentPage, totalPages)

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage && !isLoading) {
      onPageChange(page)
    }
  }

  const handlePrevious = () => {
    if (currentPage > 1 && !isLoading) {
      onPageChange(currentPage - 1)
    }
  }

  const handleNext = () => {
    if (currentPage < totalPages && !isLoading) {
      onPageChange(currentPage + 1)
    }
  }

  // 处理页面跳转输入
  const handleJumpInputChange = useCallback((value: string) => {
    setJumpValue(value)
    setJumpError('')
  }, [])

  // 验证并执行页面跳转
  const handleJumpToPage = useCallback(() => {
    const pageNum = parseInt(jumpValue.trim())

    if (!jumpValue.trim()) {
      setJumpError('Please enter page number')
      return
    }

    if (isNaN(pageNum)) {
      setJumpError('Please enter a valid number')
      return
    }

    if (pageNum < 1) {
      setJumpError('Page number cannot be less than 1')
      return
    }

    if (pageNum > totalPages) {
      setJumpError(`Page number cannot exceed ${totalPages}`)
      return
    }

    if (pageNum === currentPage) {
      setJumpError('Already on current page')
      return
    }

    // 清除错误并执行跳转
    setJumpError('')
    setJumpValue('')
    onPageChange(pageNum)
  }, [jumpValue, totalPages, currentPage, onPageChange])

  // 处理回车键跳转
  const handleJumpKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage()
    }
  }, [handleJumpToPage])

  if (totalPages <= 1) {
    return (
      <div className="flex items-center justify-between px-2">
        <div className="text-sm text-muted-foreground">
          Showing {startItem} to {endItem} of {totalItems} entries
        </div>
        <div className="text-sm text-muted-foreground">
          Page {currentPage} of {totalPages}
        </div>
      </div>
    )
  }

  return (
    <div className="px-2">
      {/* 移动端布局 */}
      <div className="sm:hidden">
        {/* 上方：数据条目信息和页面跳转 */}
        <div className="flex items-center justify-between mb-3">
          {/* 左侧：数据条目信息 */}
          <div className="text-xs text-muted-foreground">
            {startItem}-{endItem} of {totalItems}
          </div>

          {/* 右侧：页面跳转 */}
          {totalPages > 1 && (
            <div className="flex items-center space-x-1">
              <span className="text-xs text-muted-foreground">Go to</span>
              <div className="relative">
                <Input
                  type="text"
                  value={jumpValue}
                  onChange={(e) => handleJumpInputChange(e.target.value)}
                  onKeyDown={handleJumpKeyDown}
                  placeholder="Page"
                  className={`w-12 h-6 text-center text-xs ${
                    jumpError ? 'border-red-500 focus:border-red-500' : ''
                  }`}
                  disabled={isLoading}
                />
                {jumpError && (
                  <div className="absolute top-full right-0 mt-1 text-xs text-red-500 whitespace-nowrap z-10 bg-white border border-red-200 rounded px-1 py-0.5 shadow-sm">
                    {jumpError}
                  </div>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleJumpToPage}
                disabled={isLoading || !jumpValue.trim()}
                className="h-6 px-2 text-xs"
              >
                Go
              </Button>
            </div>
          )}
        </div>

        {/* 下方：分页控件居中 */}
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevious}
            disabled={currentPage <= 1 || isLoading}
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <div className="text-sm font-medium">
            {currentPage} / {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNext}
            disabled={currentPage >= totalPages || isLoading}
          >
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 桌面端布局 */}
      <div className="hidden sm:flex sm:items-center sm:justify-between">
        {/* 左侧：显示条目信息 */}
        <div className="text-sm text-muted-foreground">
          Showing {startItem} to {endItem} of {totalItems} entries
        </div>

        {/* 右侧：分页控件 */}
        <div className="flex items-center space-x-2">

          <div className="flex items-center space-x-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={handlePrevious}
                    className={
                      currentPage <= 1 || isLoading
                        ? 'pointer-events-none opacity-50'
                        : 'cursor-pointer'
                    }
                  />
                </PaginationItem>

                {pageNumbers.map((page, index) => (
                  <PaginationItem key={index}>
                    {page === 'ellipsis' ? (
                      <PaginationEllipsis />
                    ) : (
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={page === currentPage}
                        className={
                          isLoading
                            ? 'pointer-events-none opacity-50'
                            : 'cursor-pointer'
                        }
                      >
                        {page}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={handleNext}
                    className={
                      currentPage >= totalPages || isLoading
                        ? 'pointer-events-none opacity-50'
                        : 'cursor-pointer'
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>

            {/* 页面跳转功能 */}
            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground whitespace-nowrap">Go to</span>
                <div className="relative">
                  <Input
                    type="text"
                    value={jumpValue}
                    onChange={(e) => handleJumpInputChange(e.target.value)}
                    onKeyDown={handleJumpKeyDown}
                    placeholder="Page"
                    className={`w-16 h-8 text-center text-sm ${
                      jumpError ? 'border-red-500 focus:border-red-500' : ''
                    }`}
                    disabled={isLoading}
                  />
                  {jumpError && (
                    <div className="absolute top-full left-0 mt-1 text-xs text-red-500 whitespace-nowrap z-10 bg-white border border-red-200 rounded px-2 py-1 shadow-sm">
                      {jumpError}
                    </div>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleJumpToPage}
                  disabled={isLoading || !jumpValue.trim()}
                  className="h-8 px-3 text-sm"
                >
                  Go
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
