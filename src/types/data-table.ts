// 基础列筛选类型
export interface ColumnFilter {
  id: string
  value: unknown
}

// 基础列排序类型
export interface ColumnSort {
  id: string
  desc: boolean
}

// 扩展的列筛选类型
export interface ExtendedColumnFilter extends ColumnFilter {
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte'
  variant?: string
}

// 扩展的列排序类型
export interface ExtendedColumnSort extends ColumnSort {
  priority?: number
}

// 数据表格状态
export interface DataTableState {
  pagination: {
    pageIndex: number
    pageSize: number
  }
  sorting: ExtendedColumnSort[]
  columnFilters: ExtendedColumnFilter[]
  globalFilter: string
  columnVisibility: Record<string, boolean>
}

// 数据表格配置
export interface DataTableConfig {
  enableSorting?: boolean
  enableFiltering?: boolean
  enableGlobalFilter?: boolean
  enableColumnVisibility?: boolean
  enablePagination?: boolean
  enableMultiSort?: boolean
  enableRowSelection?: boolean
  manualPagination?: boolean
  manualSorting?: boolean
  manualFiltering?: boolean
}

// 数据表格回调函数
export interface DataTableCallbacks<TData> {
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void
  onSortingChange?: (sorting: ExtendedColumnSort[]) => void
  onColumnFiltersChange?: (filters: ExtendedColumnFilter[]) => void
  onGlobalFilterChange?: (filter: string) => void
  onColumnVisibilityChange?: (visibility: Record<string, boolean>) => void
  onRowSelectionChange?: (selection: Record<string, boolean>) => void
  onRowClick?: (row: TData) => void
  onRowDoubleClick?: (row: TData) => void
}

// 数据表格属性
export interface DataTableProps<TData> extends DataTableConfig, DataTableCallbacks<TData> {
  data: TData[]
  columns: any[] // 使用 any 以兼容不同的列定义类型
  state?: Partial<DataTableState>
  loading?: boolean
  error?: string | null
  className?: string
  emptyMessage?: string
  loadingMessage?: string
  errorMessage?: string
}
