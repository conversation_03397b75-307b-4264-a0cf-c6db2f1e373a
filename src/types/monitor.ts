// 监控相关的类型定义

export interface ServerInfo {
  version: string
  started: number
  time: number
  uptime: number
  bytes_received: number
  bytes_sent: number
  clients_connected: number
  clients_disconnected: number
  clients_maximum: number
  clients_total: number
  messages_received: number
  messages_sent: number
  messages_dropped: number
  retained: number
  inflight: number
  inflight_dropped: number
  subscriptions: number
  packets_received: number
  packets_sent: number
  memory_alloc: number
  threads: number
}

export interface DashboardData {
  timestamp: string
  server_info: ServerInfo
  system_metrics: {
    cpu: {
      usage: number
      load_avg_1: number
      load_avg_5: number
      load_avg_15: number
    }
    memory: {
      total: number
      used: number
      available: number
      usage: number
    }
    disk: {
      total: number
      used: number
      available: number
      usage: number
    }
    network: {
      bytes_in: number
      bytes_out: number
      packets_in: number
      packets_out: number
    }
  }
}

export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

export interface ListResponse<T> {
  success: boolean
  message: string
  data: {
    items: T[]
    total: number
    page: number
    page_size: number
  }
}