# Topic历史记录查看功能

## 功能概述

新增了Topic历史记录查看功能，允许用户查看指定Topic的历史消息数据。该功能通过Dialog弹窗的形式展示，内嵌DataTable组件来显示历史数据。

## 功能特性

### 1. 历史数据查询
- 支持分页查询Topic历史消息
- 可配置每页显示数量（10/20/50/100条）
- 支持时间范围筛选（1小时/6小时/1天/7天/30天）
- 支持自定义时间范围查询

### 2. 数据展示
- 表格形式展示历史消息
- 显示时间戳、QoS等级、客户端ID、消息内容
- 消息内容支持JSON格式化显示
- QoS等级使用不同颜色的Badge标识

### 3. 搜索和过滤
- 支持按消息内容搜索
- 支持按客户端ID搜索
- 实时搜索，无需提交

### 4. 数据操作
- 支持数据刷新
- 支持导出为CSV格式
- 分页导航支持

## 技术实现

### 1. API集成
- 新增`getTopicData` API方法调用`/api/groups/topic-data`端点
- 支持分页、时间范围等查询参数
- 集成React Query进行数据缓存和状态管理

### 2. 组件结构
```
TopicHistoryDialog
├── 控制栏
│   ├── 时间范围选择器
│   ├── 每页数量选择器
│   ├── 刷新按钮
│   └── 导出按钮
├── 搜索框
├── 数据表格
│   ├── 时间戳列
│   ├── QoS等级列
│   ├── 客户端ID列
│   └── 消息内容列
└── 分页组件
```

### 3. 类型定义
新增了以下TypeScript类型：
- `TopicMessage`: 单条消息数据结构
- `TopicDataResponse`: API响应数据结构
- `GetTopicDataParams`: 查询参数结构

## 使用方法

### 1. 在Topic管理页面
1. 进入任意分组的详情页面
2. 在Topic列表中找到要查看历史的Topic
3. 点击Topic行右侧的历史记录按钮（History图标）
4. 在弹出的对话框中查看历史数据

### 2. 功能操作
- **时间范围选择**: 使用顶部的下拉菜单选择查询时间范围
- **搜索**: 在搜索框中输入关键词进行实时搜索
- **分页**: 使用底部分页组件浏览不同页面的数据
- **导出**: 点击导出按钮将当前筛选的数据导出为CSV文件
- **刷新**: 点击刷新按钮重新加载最新数据

## 文件结构

### 新增文件
- `src/features/groups/components/topic-history-dialog.tsx` - 历史记录对话框组件

### 修改文件
- `src/types/groups.ts` - 新增Topic数据相关类型定义
- `src/api/groups.ts` - 新增Topic数据API方法
- `src/features/groups/components/topic-management.tsx` - 添加历史记录按钮

## API依赖

该功能依赖于后端提供的Topic数据查询API：

```
GET /api/groups/topic-data
```

查询参数：
- `topic` (必填): Topic名称
- `pagesize` (可选): 每页大小，默认10
- `page` (可选): 页码，默认1
- `timerange` (可选): 时间范围，如"1h", "24h", "7d"
- `start_time` (可选): 开始时间，RFC3339格式
- `end_time` (可选): 结束时间，RFC3339格式

## 注意事项

1. **权限控制**: 用户需要有相应Topic的查看权限才能查看历史数据
2. **性能考虑**: 大量历史数据查询可能较慢，建议合理设置时间范围
3. **数据格式**: 消息内容会尝试JSON格式化，如果不是有效JSON则原样显示
4. **导出限制**: 导出功能仅导出当前筛选和分页的数据，不是全部数据

## 未来扩展

可以考虑的功能扩展：
1. 支持更多时间范围选项
2. 支持按消息大小、QoS等级筛选
3. 支持消息内容的高级搜索（正则表达式等）
4. 支持批量导出所有数据
5. 支持实时消息监控
6. 支持消息统计图表展示
