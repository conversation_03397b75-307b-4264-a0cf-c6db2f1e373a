# MQTT预警系统实现文档

## 概述

根据新增的预警规则API文档，为 `src/features/groups/` 中的topic创建了完整的预警管理功能，包括预警规则管理dialog和预警记录查看dialog。

## 实现的功能

### 1. 预警规则管理 (AlertRulesDialog)

**文件位置**: `src/features/groups/components/alert-rules-dialog.tsx`

**主要功能**:
- 显示当前topic的所有预警规则列表
- 支持创建新的预警规则
- 支持编辑现有规则
- 支持删除规则
- 支持测试规则
- 支持启用/禁用规则
- 支持搜索和分页

**界面特性**:
- 表格形式展示规则列表
- 每行显示规则名称、类型、级别、状态、创建时间
- 操作菜单包含测试、启用/禁用、编辑、删除功能
- 响应式设计，支持移动端

### 2. 预警规则表单 (AlertRuleFormDialog)

**文件位置**: `src/features/groups/components/alert-rule-form-dialog.tsx`

**主要功能**:
- 创建和编辑预警规则的表单界面
- 支持多种规则类型（阈值、变化率、缺失数据、异常值、复合条件）
- 动态条件配置，支持添加/删除多个条件
- 频率控制设置
- 通知配置管理

**表单结构**:
1. **基本信息**: 规则名称、类型、描述、级别、启用状态
2. **预警条件**: 字段名、数据类型、操作符、值（支持多条件）
3. **频率控制**: 最大预警次数、时间窗口
4. **通知配置**: 启用状态、通知渠道、接收人

**条件配置特性**:
- 支持数值型（>、<、>=、<=、==、!=）
- 支持字符串型（等于、包含、开始于、结束于）
- 支持布尔型（==、!=）
- 动态操作符选择，根据数据类型自动更新

### 3. 预警记录查看 (AlertRecordsDialog)

**文件位置**: `src/features/groups/components/alert-records-dialog.tsx`

**主要功能**:
- 显示当前topic的预警记录列表
- 预警统计信息展示
- 支持多维度过滤（时间范围、级别、状态）
- 支持搜索和导出功能

**统计信息**:
- 总预警数
- 待处理预警数
- 已处理预警数
- 已忽略预警数

**过滤功能**:
- 时间范围：1小时、6小时、1天、7天、30天
- 预警级别：信息、警告、错误、严重、灾难
- 处理状态：待处理、已处理、已忽略
- 关键词搜索：规则名称、触发数据

### 4. Topic管理集成

**文件位置**: `src/features/groups/components/topic-management.tsx`

**集成功能**:
- 在topic操作菜单中添加"预警规则"和"预警记录"选项
- 与现有的历史数据、权限管理功能并列
- 保持一致的用户体验

## 技术实现

### 1. 类型定义

**文件位置**: `src/types/groups.ts`

**新增类型**:
```typescript
- AlertCondition: 预警条件
- FrequencyLimit: 频率限制
- NotificationConfig: 通知配置
- AlertRule: 预警规则实体
- AlertRecord: 预警记录实体
- AlertStatistics: 预警统计信息
- CreateAlertRuleRequest: 创建预警规则请求
- UpdateAlertRuleRequest: 更新预警规则请求
- GetAlertRulesParams: 获取预警规则参数
- GetAlertRecordsParams: 获取预警记录参数
- TestAlertRuleRequest: 测试预警规则请求
```

### 2. API集成

**文件位置**: `src/api/groups.ts`

**新增API方法**:
```typescript
- createAlertRule: 创建预警规则
- getAlertRules: 获取预警规则列表
- getAlertRule: 获取预警规则详情
- updateAlertRule: 更新预警规则
- deleteAlertRule: 删除预警规则
- testAlertRule: 测试预警规则
- getAlertRecords: 获取预警记录列表
- getAlertStatistics: 获取预警统计信息
```

**新增查询键**:
```typescript
- alertRules: 预警规则查询键
- alertRule: 单个预警规则查询键
- alertRecords: 预警记录查询键
- alertStatistics: 预警统计查询键
```

### 3. 组件架构

**设计原则**:
- 遵循现有代码风格和组件结构
- 使用统一的UI组件库
- 实现响应式设计
- 完善的错误处理和加载状态
- 支持国际化（中文）

**组件层次**:
```
TopicManagement
├── AlertRulesDialog
│   └── AlertRuleFormDialog
└── AlertRecordsDialog
```

## 使用方式

### 1. 在Topic管理页面

1. 进入分组详情页面
2. 在Topic列表中，点击任意topic的操作菜单
3. 选择"预警规则"或"预警记录"选项
4. 在弹出的dialog中进行相应操作

### 2. 预警规则管理

1. 点击"创建预警规则"按钮
2. 填写规则基本信息
3. 配置预警条件（可添加多个条件）
4. 设置频率控制参数
5. 配置通知设置
6. 保存规则

### 3. 预警记录查看

1. 查看统计信息卡片
2. 使用过滤条件筛选记录
3. 搜索特定内容
4. 导出记录数据

## 演示功能

**文件位置**: `src/features/groups/components/alert-demo.tsx`

提供了一个演示组件，可以用来测试预警功能的界面和交互，包含模拟数据和功能说明。

## 注意事项

1. **API依赖**: 功能依赖于后端API的实现，需要确保API端点正常工作
2. **权限控制**: 只有分组创建者可以管理预警规则，所有成员都可以查看预警记录
3. **数据验证**: 表单包含完整的数据验证，确保输入数据的正确性
4. **错误处理**: 实现了完善的错误处理机制，提供用户友好的错误提示
5. **性能优化**: 使用React Query进行数据缓存和状态管理，提高性能

## 扩展性

该实现具有良好的扩展性，可以轻松添加：
- 新的预警规则类型
- 更多的通知渠道
- 高级的条件配置
- 预警记录的批量操作
- 预警趋势分析图表

## 最新修复 (2024-07-26)

### 界面优化
- **修改原生Dialog组件宽度**:
  - 修改 `src/components/ui/dialog.tsx` 中的 DialogContent 组件
  - 将默认最大宽度从 `sm:max-w-lg` (512px) 改为 `sm:max-w-[800px]`
  - 所有使用 Dialog 组件的地方自动继承新的宽度设置
  - 移动端仍然使用 `max-w-[calc(100%-2rem)]` 保持响应式
  - 提供更宽敞的操作空间，统一的用户体验



### Bug修复
- **修复Select组件空值错误**:
  - 问题: `A <Select.Item /> must have a value prop that is not an empty string`
  - 解决: 将空字符串值改为 `"all"` 值
  - 影响: 预警记录dialog中的级别和状态过滤器
  - 修复文件: `src/features/groups/components/alert-records-dialog.tsx`

### 修复详情

**Dialog宽度修改**:
```typescript
// 修改前 (src/components/ui/dialog.tsx)
className={cn(
  "... sm:max-w-lg", // 512px
  className
)}

// 修改后
className={cn(
  "... sm:max-w-[800px]", // 800px
  className
)}
```

**Select组件空值修复**:
```typescript
// 修复前
const [levelFilter, setLevelFilter] = useState<string>('')
<SelectItem value="">全部</SelectItem>

// 修复后
const [levelFilter, setLevelFilter] = useState<string>('all')
<SelectItem value="all">全部</SelectItem>
```

## 总结

成功实现了完整的MQTT预警系统前端功能，包括预警规则的增删改查和预警记录的查询展示。功能完善，界面友好，代码结构清晰，易于维护和扩展。经过最新修复，解决了界面宽度和Select组件的问题，用户体验更加流畅。
