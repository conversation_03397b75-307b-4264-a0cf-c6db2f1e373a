# Topic数据管理API文档

本文档描述了新增的Topic数据查询和删除API功能。

## API概述

新增了两个API端点，用于管理Topic数据：

1. `GET /api/groups/topic-data` - 查询Topic数据
2. `DELETE /api/groups/topic-data` - 删除Topic数据

这些API都位于`/api/groups`分组下，需要JWT认证和Casbin权限控制。

## 1. 查询Topic数据

### 端点
```
GET /api/groups/topic-data
```

### 请求参数

#### 必填参数
- `topic` (string): Topic名称

#### 可选参数
- `pagesize` (int): 每页大小，默认10，最大100
- `page` (int): 页码，默认1
- `timerange` (string): 时间范围，支持格式如 "1d", "2h", "30m"，默认1天
- `start_time` (string): 开始时间，RFC3339格式，优先级高于timerange
- `end_time` (string): 结束时间，RFC3339格式，优先级高于timerange

### 请求示例

#### 基本查询（使用默认参数）
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 分页查询
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&pagesize=20&page=2" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 指定时间范围查询
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&timerange=2h" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 指定具体时间查询
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 0,
  "message": "查询Topic数据成功",
  "data": {
    "messages": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "topic": "sensor/temperature",
        "payload": "{\"temperature\": 25.5}",
        "qos": 1,
        "client_id": "sensor001",
        "group_id": "group1"
      }
    ],
    "total": 150,
    "page": 1,
    "page_size": 10,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-01-01T23:59:59Z"
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "topic参数不能为空"
}
```

## 2. 删除Topic数据

### 端点
```
DELETE /api/groups/topic-data
```

### 请求参数

#### 必填参数
- `topic` (string): Topic名称
- `start_time` (string): 开始时间，RFC3339格式
- `end_time` (string): 结束时间，RFC3339格式

### 请求示例

```bash
curl -X DELETE "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 0,
  "message": "删除Topic数据成功",
  "data": {
    "topic": "sensor/temperature",
    "deleted_count": 1250,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-01-01T23:59:59Z"
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "开始时间和结束时间不能为空"
}
```

## 错误码说明

- `400`: 参数错误
- `401`: 未认证
- `403`: 权限不足
- `500`: 服务器内部错误
- `503`: TDengine服务不可用

## 注意事项

1. 所有API都需要JWT认证和相应的权限
2. 时间参数必须使用RFC3339格式 (例如: 2024-01-01T12:00:00Z)
3. 查询API支持分页，建议合理设置pagesize避免一次查询过多数据
4. 删除操作不可逆，请谨慎使用
5. timerange参数支持的格式：
   - 秒: "30s"
   - 分钟: "30m"
   - 小时: "2h"
   - 天: "1d" (注意：Go的time.ParseDuration不直接支持天，需要使用"24h"代替"1d")

## 实现细节

- 查询API默认按时间倒序排列（最新的数据在前）
- 删除API会先查询要删除的记录数，然后执行删除操作
- 所有时间相关的操作都基于TDengine的时间戳字段
- API集成在现有的groups路由组中，复用了认证和权限控制机制
