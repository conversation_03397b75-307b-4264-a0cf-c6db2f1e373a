# 设备管理 API 文档

## 概述

设备管理功能提供了完整的设备和设备类型的增删改查操作。支持多租户架构，每个用户只能管理自己的设备。

## 预定义设备类型

系统预定义了以下设备类型：
- **BC-3GM-R**: 3G模块设备
- **BC-4GM-C**: 4G模块设备  
- **BC-ECR-C**: ECR控制器设备
- **BGTR**: GPS追踪器设备

## 设备字段

每个设备包含以下字段：
- `device_type`: 设备类型（必须是已存在的设备类型）
- `device_name`: 设备名称
- `serial_number`: 序列号（用户范围内唯一）
- `imei_code`: IMEI码（用户范围内唯一）
- `status`: 设备状态（active/inactive/maintenance）

## API 端点

### 设备管理

#### 1. 创建设备
```
POST /api/devices
```

**请求体：**
```json
{
  "device_type": "BC-3GM-R",
  "device_name": "测试设备001",
  "serial_number": "SN001",
  "imei_code": "IMEI001"
}
```

**响应：**
```json
{
  "success": true,
  "message": "设备创建成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "device_type": "BC-3GM-R",
    "device_name": "测试设备001",
    "serial_number": "SN001",
    "imei_code": "IMEI001",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 2. 获取设备列表
```
GET /api/devices?page=1&page_size=10&device_type=BC-3GM-R&status=active&search=测试
```

**查询参数：**
- `page`: 页码（默认：1）
- `page_size`: 每页数量（默认：10）
- `device_type`: 设备类型过滤
- `status`: 状态过滤
- `search`: 搜索关键词（搜索设备名称和序列号）

**响应：**
```json
{
  "success": true,
  "message": "获取设备列表成功",
  "data": {
    "devices": [
      {
        "id": "507f1f77bcf86cd799439011",
        "device_type": "BC-3GM-R",
        "device_name": "测试设备001",
        "serial_number": "SN001",
        "imei_code": "IMEI001",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10
  }
}
```

#### 3. 更新设备
```
PUT /api/devices/{id}
```

**请求体：**
```json
{
  "device_name": "更新后的设备名称",
  "status": "maintenance"
}
```

**响应：**
```json
{
  "success": true,
  "message": "设备更新成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "device_type": "BC-3GM-R",
    "device_name": "更新后的设备名称",
    "serial_number": "SN001",
    "imei_code": "IMEI001",
    "status": "maintenance",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:01Z"
  }
}
```

#### 4. 删除设备
```
DELETE /api/devices/{id}
```

**响应：**
```json
{
  "success": true,
  "message": "设备删除成功"
}
```

### 设备类型管理

#### 1. 创建设备类型
```
POST /api/device-types
```

**请求体：**
```json
{
  "name": "NEW-DEVICE-TYPE",
  "description": "新设备类型描述"
}
```

**响应：**
```json
{
  "success": true,
  "message": "设备类型创建成功",
  "data": {
    "id": "507f1f77bcf86cd799439012",
    "name": "NEW-DEVICE-TYPE",
    "description": "新设备类型描述",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 2. 获取设备类型列表
```
GET /api/device-types?page=1&page_size=10
```

**响应：**
```json
{
  "success": true,
  "message": "获取设备类型列表成功",
  "data": {
    "device_types": [
      {
        "id": "507f1f77bcf86cd799439012",
        "name": "BC-3GM-R",
        "description": "3G模块设备",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 4,
    "page": 1,
    "size": 10
  }
}
```

#### 3. 更新设备类型
```
PUT /api/device-types/{id}
```

#### 4. 删除设备类型
```
DELETE /api/device-types/{id}
```

## 错误处理

### 常见错误码

- `400`: 请求参数无效
- `401`: 未授权（需要登录）
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突（如序列号或IMEI码已存在）
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 权限说明

- **设备管理**: 所有认证用户都可以管理自己的设备
- **设备类型管理**: 需要管理员权限（后续可通过Casbin配置）

## 数据验证

### 设备验证规则
- 设备类型必须存在
- 序列号在用户范围内唯一
- IMEI码在用户范围内唯一
- 设备名称不能为空

### 设备类型验证规则
- 设备类型名称全局唯一
- 名称不能为空

## 数据库索引

系统自动创建以下索引以优化查询性能：

### 设备集合索引
- `{user_id: 1, serial_number: 1}` (唯一)
- `{user_id: 1, imei_code: 1}` (唯一)
- `{user_id: 1, device_type: 1}`
- `{user_id: 1, status: 1}`
- `{user_id: 1, created_at: -1}`

### 设备类型集合索引
- `{name: 1}` (唯一)
- `{created_at: -1}`
