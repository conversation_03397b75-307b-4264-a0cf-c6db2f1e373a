# WireGuard VPN 管理系统 API 文档

## 概述

WireGuard VPN 管理系统提供了完整的 VPN 服务器和客户端管理功能，支持用户自助管理和管理员监控。系统基于网络命名空间实现用户隔离，确保安全性和资源独立性。

## 基础信息

- **Base URL**: `http://your-domain.com/api`
- **认证方式**: JWT Bearer Token
- **Content-Type**: `application/json`
- **API 版本**: v1.0

## 认证

所有 API 请求都需要在 Header 中包含有效的 JWT Token：

```
Authorization: Bearer <your-jwt-token>
```

## 响应格式

所有 API 响应都遵循统一的格式：

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 状态码

- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `500` - 服务器内部错误

---

## 用户 API

### 1. 创建 WireGuard 服务器

创建用户专属的 WireGuard VPN 服务器。

**请求**
```
POST /wireguard/server
```

**请求体**
```json
{
  "server_name": "我的VPN服务器"
}
```

**响应**
```json
{
  "success": true,
  "message": "WireGuard服务器创建成功",
  "data": {
    "server_id": "server_123456",
    "user_id": "user_789",
    "server_name": "我的VPN服务器",
    "namespace_name": "wg_user_789",
    "public_key": "abcd1234...",
    "server_address": "********/24",
    "listen_port": 51820,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取 WireGuard 服务器

获取当前用户的 WireGuard 服务器信息。

**请求**
```
GET /wireguard/server
```

**响应**
```json
{
  "success": true,
  "message": "获取WireGuard服务器成功",
  "data": {
    "server_id": "server_123456",
    "user_id": "user_789",
    "server_name": "我的VPN服务器",
    "namespace_name": "wg_user_789",
    "public_key": "abcd1234...",
    "server_address": "********/24",
    "listen_port": 51820,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 删除 WireGuard 服务器

删除当前用户的 WireGuard 服务器及其所有相关资源。

**请求**
```
DELETE /wireguard/server
```

**响应**
```json
{
  "success": true,
  "message": "WireGuard服务器删除成功",
  "data": null
}
```

### 4. 获取服务器状态

获取 WireGuard 服务器的实时运行状态。

**请求**
```
GET /wireguard/server/status
```

**响应**
```json
{
  "success": true,
  "message": "获取服务器状态成功",
  "data": {
    "server_id": "server_123456",
    "status": "active",
    "public_key": "abcd1234...",
    "listen_port": 51820,
    "peer_count": 2,
    "last_updated": "2024-01-01T00:00:00Z",
    "peers": [
      {
        "public_key": "peer_key_1...",
        "endpoint": "*************:12345",
        "allowed_ips": "********/32",
        "last_handshake": "2024-01-01T00:00:00Z",
        "bytes_in": 1024000,
        "bytes_out": 2048000
      }
    ]
  }
}
```

---

## 节点管理 API

### 5. 添加节点

为 WireGuard 服务器添加客户端节点。

**请求**
```
POST /wireguard/peers
```

**请求体**
```json
{
  "peer_name": "我的手机",
  "public_key": "peer_public_key...",
  "endpoint": "*************:12345"
}
```

**响应**
```json
{
  "success": true,
  "message": "WireGuard节点添加成功",
  "data": {
    "peer_id": "peer_123456",
    "server_id": "server_123456",
    "peer_name": "我的手机",
    "public_key": "peer_public_key...",
    "allowed_ips": ["********/32"],
    "endpoint": "*************:12345",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6. 获取节点列表

获取当前用户 WireGuard 服务器的所有节点。

**请求**
```
GET /wireguard/peers
```

**响应**
```json
{
  "success": true,
  "message": "获取节点列表成功",
  "data": [
    {
      "peer_id": "peer_123456",
      "server_id": "server_123456",
      "peer_name": "我的手机",
      "public_key": "peer_public_key...",
      "allowed_ips": ["********/32"],
      "endpoint": "*************:12345",
      "status": "active",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 7. 获取节点信息

获取指定节点的详细信息。

**请求**
```
GET /wireguard/peers/{peer_id}
```

**路径参数**
- `peer_id` (string): 节点ID

**响应**
```json
{
  "success": true,
  "message": "获取节点信息成功",
  "data": {
    "peer_id": "peer_123456",
    "server_id": "server_123456",
    "peer_name": "我的手机",
    "public_key": "peer_public_key...",
    "allowed_ips": ["********/32"],
    "endpoint": "*************:12345",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 8. 更新节点

更新指定节点的信息。

**请求**
```
PUT /wireguard/peers/{peer_id}
```

**路径参数**
- `peer_id` (string): 节点ID

**请求体**
```json
{
  "peer_name": "我的新手机",
  "endpoint": "*************:12345"
}
```

**响应**
```json
{
  "success": true,
  "message": "节点更新成功",
  "data": {
    "peer_id": "peer_123456",
    "server_id": "server_123456",
    "peer_name": "我的新手机",
    "public_key": "peer_public_key...",
    "allowed_ips": ["********/32"],
    "endpoint": "*************:12345",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T01:00:00Z"
  }
}
```

### 9. 删除节点

删除指定的节点。

**请求**
```
DELETE /wireguard/peers/{peer_id}
```

**路径参数**
- `peer_id` (string): 节点ID

**响应**
```json
{
  "success": true,
  "message": "节点删除成功",
  "data": null
}
```

---

## 配置管理 API

### 10. 获取服务器配置

获取 WireGuard 服务器的配置文件。

**请求**
```
GET /wireguard/config/server
```

**响应**
```
Content-Type: text/plain
Content-Disposition: attachment; filename=wg0.conf

[Interface]
PrivateKey = server_private_key...
Address = ********/24
ListenPort = 51820
DNS = *******,*******

[Peer]
PublicKey = peer_public_key...
AllowedIPs = ********/32
Endpoint = *************:12345
```

### 11. 获取节点配置

获取指定节点的配置文件。

**请求**
```
GET /wireguard/config/peer/{peer_id}
```

**路径参数**
- `peer_id` (string): 节点ID

**响应**
```
Content-Type: text/plain
Content-Disposition: attachment; filename=peer.conf

[Interface]
PrivateKey = peer_private_key...
Address = ********/32
DNS = *******,*******

[Peer]
PublicKey = server_public_key...
AllowedIPs = 0.0.0.0/0
Endpoint = your-server.com:51820
PersistentKeepalive = 25
```

### 12. 同步配置

同步数据库配置到系统实际配置。

**请求**
```
POST /wireguard/sync
```

**响应**
```json
{
  "success": true,
  "message": "配置同步成功",
  "data": null
}
```

---

## 管理员 API

管理员 API 需要额外的管理员权限验证。

### 13. 获取所有服务器列表

管理员获取系统中所有用户的 WireGuard 服务器。

**请求**
```
GET /admin/wireguard/servers?page=1&page_size=20&user_id=user_123&status=active
```

**查询参数**
- `page` (int): 页码，默认 1
- `page_size` (int): 每页数量，默认 20，最大 100
- `user_id` (string): 用户ID过滤，可选
- `status` (string): 状态过滤，可选 (active, inactive, maintenance, error)

**响应**
```json
{
  "success": true,
  "message": "获取服务器列表成功",
  "data": {
    "servers": [
      {
        "server_id": "server_123456",
        "user_id": "user_789",
        "server_name": "用户VPN服务器",
        "namespace_name": "wg_user_789",
        "public_key": "abcd1234...",
        "server_address": "********/24",
        "listen_port": 51820,
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 14. 获取指定服务器信息

管理员获取指定服务器的详细信息。

**请求**
```
GET /admin/wireguard/servers/{server_id}
```

**路径参数**
- `server_id` (string): 服务器ID

**响应**
```json
{
  "success": true,
  "message": "获取服务器信息成功",
  "data": {
    "server_id": "server_123456",
    "user_id": "user_789",
    "server_name": "用户VPN服务器",
    "namespace_name": "wg_user_789",
    "public_key": "abcd1234...",
    "server_address": "********/24",
    "listen_port": 51820,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 15. 删除指定服务器

管理员删除指定的 WireGuard 服务器。

**请求**
```
DELETE /admin/wireguard/servers/{server_id}
```

**路径参数**
- `server_id` (string): 服务器ID

**响应**
```json
{
  "success": true,
  "message": "服务器删除成功",
  "data": null
}
```

### 16. 获取系统状态

管理员获取 WireGuard 系统的整体状态和统计信息。

**请求**
```
GET /admin/wireguard/system/status
```

**响应**
```json
{
  "success": true,
  "message": "获取系统状态成功",
  "data": {
    "total_servers": 10,
    "active_servers": 8,
    "inactive_servers": 2,
    "total_peers": 25,
    "active_peers": 20,
    "total_users": 10,
    "port_usage": {
      "total_ports": 1000,
      "used_ports": 10,
      "available_ports": 990
    },
    "subnet_usage": {
      "total_subnets": 256,
      "used_subnets": 10,
      "available_subnets": 246
    },
    "system_health": "healthy",
    "last_updated": "2024-01-01T00:00:00Z"
  }
}
```

---

## 数据模型

### WireGuardServerResponse
```json
{
  "server_id": "string",
  "user_id": "string",
  "server_name": "string",
  "namespace_name": "string",
  "public_key": "string",
  "server_address": "string",
  "listen_port": "integer",
  "status": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### WireGuardPeerResponse
```json
{
  "peer_id": "string",
  "server_id": "string",
  "peer_name": "string",
  "public_key": "string",
  "allowed_ips": ["string"],
  "endpoint": "string",
  "status": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### CreateWireGuardServerRequest
```json
{
  "server_name": "string (required, 1-50 chars)"
}
```

### CreateWireGuardPeerRequest
```json
{
  "peer_name": "string (required, 1-50 chars)",
  "public_key": "string (required)",
  "endpoint": "string (optional)"
}
```

### UpdateWireGuardPeerRequest
```json
{
  "peer_name": "string (optional, 1-50 chars)",
  "endpoint": "string (optional)"
}
```

---

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `UNAUTHORIZED` | 401 | 未授权访问，需要登录 |
| `FORBIDDEN` | 403 | 权限不足，需要管理员权限 |
| `SERVER_NOT_FOUND` | 404 | WireGuard服务器不存在 |
| `PEER_NOT_FOUND` | 404 | WireGuard节点不存在 |
| `SERVER_ALREADY_EXISTS` | 409 | 用户已有WireGuard服务器 |
| `PEER_NAME_EXISTS` | 409 | 节点名称已存在 |
| `PEER_PUBLIC_KEY_EXISTS` | 409 | 节点公钥已存在 |
| `PORT_UNAVAILABLE` | 409 | 端口不可用 |
| `SUBNET_UNAVAILABLE` | 409 | 子网不可用 |
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

---

## 使用示例

### 完整的 VPN 设置流程

1. **创建服务器**
```bash
curl -X POST http://your-domain.com/api/wireguard/server \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"server_name": "我的VPN服务器"}'
```

2. **添加客户端节点**
```bash
curl -X POST http://your-domain.com/api/wireguard/peers \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "peer_name": "我的手机",
    "public_key": "your-peer-public-key",
    "endpoint": "*************:12345"
  }'
```

3. **获取服务器配置**
```bash
curl -X GET http://your-domain.com/api/wireguard/config/server \
  -H "Authorization: Bearer your-jwt-token" \
  -o server.conf
```

4. **获取客户端配置**
```bash
curl -X GET http://your-domain.com/api/wireguard/config/peer/peer_123456 \
  -H "Authorization: Bearer your-jwt-token" \
  -o client.conf
```

5. **同步配置**
```bash
curl -X POST http://your-domain.com/api/wireguard/sync \
  -H "Authorization: Bearer your-jwt-token"
```

---

## 注意事项

1. **网络隔离**: 每个用户的 WireGuard 服务器运行在独立的网络命名空间中，确保用户间的网络隔离。

2. **资源限制**:
   - 每个用户只能创建一个 WireGuard 服务器
   - 端口范围: 51820-52820
   - 子网范围: 10.8.0.0/16

3. **安全性**:
   - 所有 API 都需要 JWT 认证
   - 管理员 API 需要额外的权限验证
   - 公钥必须唯一，不能重复使用

4. **配置同步**: 修改配置后需要调用同步 API 使配置生效。

5. **状态管理**: 服务器和节点都有状态管理，支持启用/禁用操作。

---

## 技术支持

如有问题，请联系技术支持团队或查看系统日志获取详细错误信息。
