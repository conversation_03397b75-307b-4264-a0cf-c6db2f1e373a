# 新增API功能说明文档

## 概述

本文档详细说明了系统中新增的API功能，包括Topic数据管理和MQTT预警系统两大模块。所有API都集成在`/api/groups`路由组下，需要JWT认证和权限控制。

## 目录

1. [Topic数据管理API](#topic数据管理api)
2. [MQTT预警系统API](#mqtt预警系统api)
3. [认证和权限](#认证和权限)
4. [错误处理](#错误处理)
5. [使用示例](#使用示例)

---

## Topic数据管理API

### 功能概述
提供对MQTT Topic数据的查询和删除功能，支持灵活的时间范围和分页查询。

### API端点

#### 1. 查询Topic数据
- **端点**: `GET /api/groups/topic-data`
- **功能**: 查询指定Topic的历史消息数据
- **参数**:
  - `topic` (必填): Topic名称
  - `pagesize` (可选): 每页大小，默认10，最大100
  - `page` (可选): 页码，默认1
  - `timerange` (可选): 时间范围，如"1h", "2d"，默认1天
  - `start_time` (可选): 开始时间，RFC3339格式
  - `end_time` (可选): 结束时间，RFC3339格式

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&pagesize=20&page=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "查询Topic数据成功",
  "data": {
    "messages": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "topic": "sensor/temperature",
        "payload": "{\"temperature\": 25.5}",
        "qos": 1,
        "client_id": "sensor001",
        "group_id": "group1"
      }
    ],
    "total": 150,
    "page": 1,
    "page_size": 20
  }
}
```

#### 2. 删除Topic数据
- **端点**: `DELETE /api/groups/topic-data`
- **功能**: 删除指定Topic在指定时间范围内的数据
- **参数**:
  - `topic` (必填): Topic名称
  - `start_time` (必填): 开始时间，RFC3339格式
  - `end_time` (必填): 结束时间，RFC3339格式

**请求示例**:
```bash
curl -X DELETE "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "删除Topic数据成功",
  "data": {
    "topic": "sensor/temperature",
    "deleted_count": 1250,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-01-01T23:59:59Z"
  }
}
```

---

## MQTT预警系统API

### 功能概述
提供完整的MQTT数据预警功能，包括预警规则管理、预警记录查询和统计分析。

### 预警规则管理

#### 1. 创建预警规则
- **端点**: `POST /api/groups/alert-rules`
- **功能**: 创建新的预警规则

**请求体示例**:
```json
{
  "group_id": "group123",
  "topic": "sensor/temperature",
  "rule_name": "温度过高预警",
  "rule_type": "threshold",
  "description": "当温度超过35度时触发预警",
  "conditions": [
    {
      "field": "temperature",
      "operator": ">",
      "value": "35",
      "data_type": "number"
    }
  ],
  "level": 3,
  "frequency_limit": {
    "max_count": 3,
    "time_window": 3600
  },
  "notification": {
    "enabled": true,
    "channels": ["email"],
    "recipients": ["<EMAIL>"]
  },
  "enabled": true
}
```

#### 2. 获取预警规则列表
- **端点**: `GET /api/groups/alert-rules`
- **参数**:
  - `page` (可选): 页码，默认1
  - `page_size` (可选): 每页大小，默认20，最大100
  - `group_id` (可选): 分组ID过滤
  - `topic` (可选): Topic过滤
  - `rule_type` (可选): 规则类型过滤
  - `enabled` (可选): 启用状态过滤
  - `level` (可选): 预警级别过滤
  - `search` (可选): 搜索规则名称或描述

#### 3. 获取预警规则详情
- **端点**: `GET /api/groups/alert-rules/{ruleId}`

#### 4. 更新预警规则
- **端点**: `PUT /api/groups/alert-rules/{ruleId}`

#### 5. 删除预警规则
- **端点**: `DELETE /api/groups/alert-rules/{ruleId}`

#### 6. 测试预警规则
- **端点**: `POST /api/groups/alert-rules/{ruleId}/test`
- **功能**: 使用测试数据验证预警规则是否正确

**请求体示例**:
```json
{
  "temperature": 40,
  "humidity": 60
}
```

### 预警记录管理

#### 1. 获取预警记录列表
- **端点**: `GET /api/groups/alert-records`
- **参数**:
  - `page` (可选): 页码，默认1
  - `page_size` (可选): 每页大小，默认20，最大100
  - `group_id` (可选): 分组ID过滤
  - `topic` (可选): Topic过滤
  - `rule_id` (可选): 规则ID过滤
  - `level` (可选): 预警级别过滤
  - `status` (可选): 状态过滤 (0-待处理, 1-已处理, 2-已忽略)
  - `start_time` (可选): 开始时间，RFC3339格式
  - `end_time` (可选): 结束时间，RFC3339格式

#### 2. 获取预警统计信息
- **端点**: `GET /api/groups/alert-statistics`
- **参数**:
  - `group_id` (可选): 分组ID

**响应示例**:
```json
{
  "code": 0,
  "message": "获取预警统计成功",
  "data": {
    "total_rules": 10,
    "enabled_rules": 8,
    "total_alerts": 150,
    "alerts_by_level": {
      "1": 20,
      "2": 50,
      "3": 60,
      "4": 15,
      "5": 5
    },
    "alerts_by_status": {
      "0": 30,
      "1": 100,
      "2": 20
    },
    "recent_alerts": [...],
    "top_alert_topics": [
      {
        "topic": "sensor/temperature",
        "count": 50
      }
    ]
  }
}
```

---

## 预警规则配置说明

### 规则类型
- **threshold**: 阈值预警（已实现）
- **rate**: 变化率预警（待实现）
- **missing**: 缺失数据预警（待实现）
- **anomaly**: 异常值预警（待实现）
- **composite**: 复合条件预警（待实现）

### 数据类型和操作符
- **number**: 数值型
  - 支持操作符: `>`, `<`, `>=`, `<=`, `==`, `!=`
- **string**: 字符串型
  - 支持操作符: `equals`, `contains`, `startsWith`, `endsWith`
- **bool**: 布尔型
  - 支持操作符: `==`, `!=`

### 预警级别
1. **信息 (Info)**: 一般性提醒
2. **警告 (Warning)**: 需要关注的情况
3. **错误 (Error)**: 需要处理的问题
4. **严重 (Critical)**: 紧急处理的问题
5. **灾难 (Fatal)**: 系统级严重问题

### 频率控制
- `max_count`: 时间窗口内最大预警次数
- `time_window`: 时间窗口长度（秒）
- 默认值：1小时内最多3次预警

---

## 认证和权限

所有API都需要：
1. **JWT认证**: 在请求头中包含有效的JWT token
2. **权限控制**: 通过Casbin进行权限验证

```
Authorization: Bearer YOUR_JWT_TOKEN
```

---

## 错误处理

### 通用错误码
- `0`: 成功
- `400`: 参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `500`: 服务器内部错误
- `503`: 服务不可用

### 错误响应格式
```json
{
  "code": 400,
  "message": "具体的错误描述"
}
```

---

## 使用示例

### 完整的预警流程示例

1. **创建预警规则**:
```bash
curl -X POST "http://localhost:8080/api/groups/alert-rules" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "user123",
    "topic": "sensor/temperature",
    "rule_name": "温度过高预警",
    "rule_type": "threshold",
    "conditions": [
      {
        "field": "temperature",
        "operator": ">",
        "value": "35",
        "data_type": "number"
      }
    ],
    "level": 3,
    "enabled": true
  }'
```

2. **发布MQTT消息触发预警**:
```bash
mosquitto_pub -h localhost -p 1883 -t "sensor/temperature" \
  -m '{"temperature": 40, "humidity": 60}' \
  -u user123 -P password
```

3. **查询预警记录**:
```bash
curl -X GET "http://localhost:8080/api/groups/alert-records?group_id=user123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

4. **查询Topic数据**:
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 数据管理示例

1. **查询最近2小时的数据**:
```bash
curl -X GET "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&timerange=2h" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

2. **删除指定时间范围的数据**:
```bash
curl -X DELETE "http://localhost:8080/api/groups/topic-data?topic=sensor/temperature&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 注意事项

1. **时间格式**: 所有时间参数必须使用RFC3339格式
2. **分页限制**: 建议合理设置pagesize避免一次查询过多数据
3. **删除操作**: 数据删除操作不可逆，请谨慎使用
4. **预警频率**: 预警系统有频率控制，避免预警风暴
5. **异步处理**: 预警检测是异步的，可能需要几秒钟才能看到结果
6. **权限控制**: 确保用户有足够的权限访问相应的API和数据

---

## 技术实现

### 数据存储
- **MongoDB**: 存储预警规则配置
- **TDengine**: 存储MQTT消息数据和预警记录
- **Redis**: 缓存预警规则和频率控制计数

### 性能优化
- **异步处理**: 预警检测不阻塞MQTT消息处理
- **缓存机制**: Redis缓存提高查询性能
- **批量处理**: 支持批量数据操作
- **分页查询**: 避免大量数据一次性加载

### 扩展性
- **插件化设计**: 支持自定义预警规则类型
- **多通知渠道**: 支持邮件、短信、Webhook等
- **分布式支持**: 支持多实例部署
- **监控集成**: 完整的监控和日志机制

---

## API快速参考

### Topic数据管理
```
GET    /api/groups/topic-data          # 查询Topic数据
DELETE /api/groups/topic-data          # 删除Topic数据
```

### 预警规则管理
```
POST   /api/groups/alert-rules         # 创建预警规则
GET    /api/groups/alert-rules         # 获取预警规则列表
GET    /api/groups/alert-rules/:id     # 获取预警规则详情
PUT    /api/groups/alert-rules/:id     # 更新预警规则
DELETE /api/groups/alert-rules/:id     # 删除预警规则
POST   /api/groups/alert-rules/:id/test # 测试预警规则
```

### 预警记录和统计
```
GET    /api/groups/alert-records       # 获取预警记录列表
GET    /api/groups/alert-statistics    # 获取预警统计信息
```

### 认证要求
所有API都需要在请求头中包含JWT token：
```
Authorization: Bearer YOUR_JWT_TOKEN
```
